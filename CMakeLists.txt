﻿cmake_minimum_required(VERSION 3.20)

# Project name and version
project(AlgDev3 VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Find required packages
find_package(OpenCV CONFIG REQUIRED COMPONENTS core imgproc)
find_package(Eigen3 REQUIRED)
find_package(Ceres REQUIRED)
find_package(gflags REQUIRED)
find_package(glog REQUIRED)

# Print found package information
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "Eigen3 version: ${Eigen3_VERSION}")
message(STATUS "Ceres version: ${Ceres_VERSION}")

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${EIGEN3_INCLUDE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/include)

# add_subdirectory(libcbdetect)

# Source files
set(SOURCES
    main.cpp
    CodCalc.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/board_energy.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/boards_from_corners.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/create_correlation_patch.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/filter_board.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/filter_corners.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/find_modes_meanshift.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/get_image_patch.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/get_init_location.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/grow_board.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/image_normalization_and_gradients.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/init_board.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/non_maximum_suppression.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/plot_boards.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/plot_corners.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/polynomial_fit.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/refine_corners.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/score_corners.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/weight_mask.cc
    ${CMAKE_CURRENT_SOURCE_DIR}/libcbdetect/src/find_corners.cc
)

# Header files
set(HEADERS
    CodCalc.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME} 
    ${OpenCV_LIBS}
    Eigen3::Eigen
    ceres
    gflags::gflags
    glog::glog
    #cbdetect
)

# Set target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# Platform-specific linking
if(WIN32)
    # Windows specific libraries
    target_link_libraries(${PROJECT_NAME} 
        kernel32
        user32
        gdi32
        winspool
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        advapi32
    )
endif()

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE _DEBUG)
    if(MSVC)
        set_target_properties(${PROJECT_NAME} PROPERTIES
            LINK_FLAGS "/SUBSYSTEM:CONSOLE"
        )
    endif()
endif()

# Install configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Copy OpenCV DLLs to output directory on Windows
if(WIN32 AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    # This helps with running the executable directly from the build directory
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:opencv_world>
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
        COMMENT "Copying OpenCV DLLs to output directory"
    )
endif()

# Print build information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")

# Optional: Enable testing
option(BUILD_TESTING "Build tests" OFF)
if(BUILD_TESTING)
    enable_testing()
    # Add test subdirectory if it exists
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/tests")
        add_subdirectory(tests)
    endif()
endif()

# Optional: Generate compile_commands.json for IDE support
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Package configuration
set(CPACK_PACKAGE_NAME ${PROJECT_NAME})
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Algorithm Development Project")
set(CPACK_PACKAGE_VENDOR "TXD")
include(CPack)

