language: c++

matrix:
  include:
    - name: (Xenial) gcc-5 compilation
      dist: xenial
      before_install:
        - sudo apt-get update
        - sudo apt-get install -y libopencv-core-dev libopencv-highgui-dev libopencv-imgcodecs-dev libopencv-imgproc-dev
      script:
        - mkdir build
        - cd build
        - cmake -DCMAKE_C_COMPILER="gcc" -DCMAKE_CXX_COMPILER="g++" ..
        - make
        - make clean
