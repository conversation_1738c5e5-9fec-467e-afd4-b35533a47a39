#include <opencv2/opencv.hpp>
#include <vector>
#include "gradient.h"

/**
 * @brief 计算N维数组的梯度，同时返回两个方向的梯度
 * 
 * 梯度使用二阶精度的中心差分计算内部点，在边界使用一阶或二阶精度的单侧差分（前向或后向）。
 * 返回的梯度与输入数组具有相同的形状。
 * 
 * @param f 包含标量函数样本的N维数组
 * @param edge_order 边界处梯度计算的精度阶数，可以是1或2，默认为1
 * @return 包含两个方向梯度的std::pair，first为垂直方向（y方向），second为水平方向（x方向）
 */
std::pair<cv::Mat, cv::Mat> gradient(const cv::Mat& f, int edge_order) {
    // 检查edge_order参数
    if (edge_order > 2) {
        throw std::invalid_argument("'edge_order' greater than 2 not supported");
    }
    
    // 检查输入数组的大小
    if (f.rows < edge_order + 1 || f.cols < edge_order + 1) {
        throw std::invalid_argument("Shape of array too small to calculate a numerical gradient, "
                                  "at least (edge_order + 1) elements are required in both dimensions.");
    }
    
    // 转换输入为double类型
    cv::Mat f_double;
    f.convertTo(f_double, CV_64F);
    
    // 创建结果数组
    cv::Mat grad_y = cv::Mat::zeros(f.size(), CV_64F);
    cv::Mat grad_x = cv::Mat::zeros(f.size(), CV_64F);
    
    // 使用统一间距为1.0
    double dx = 1.0;
    
    // 使用Sobel算子计算内部区域的梯度（更高效）
    cv::Mat interior_y, interior_x;
    
    // 计算内部区域的梯度
    if (f.rows > 2 && f.cols > 2) {
        // 提取内部区域（去除边界）
        cv::Rect interior_rect(1, 1, f.cols - 2, f.rows - 2);
        cv::Mat interior = f_double(interior_rect);
        
        // 使用Sobel算子计算内部梯度
        cv::Mat kernel_y = (cv::Mat_<double>(3, 1) << -1, 0, 1) / (2.0 * dx);
        cv::Mat kernel_x = (cv::Mat_<double>(1, 3) << -1, 0, 1) / (2.0 * dx);
        
        cv::filter2D(interior, interior_y, CV_64F, kernel_y);
        cv::filter2D(interior, interior_x, CV_64F, kernel_x);
        
        // 将计算结果复制到结果矩阵的内部区域
        interior_y.copyTo(grad_y(interior_rect));
        interior_x.copyTo(grad_x(interior_rect));
    } else {
        // 如果矩阵太小，使用原始方法计算内部点
        for (int i = 1; i < f.rows - 1; i++) {
            for (int j = 0; j < f.cols; j++) {
                grad_y.at<double>(i, j) = (f_double.at<double>(i + 1, j) - f_double.at<double>(i - 1, j)) / (2.0 * dx);
            }
        }
        
        for (int i = 0; i < f.rows; i++) {
            for (int j = 1; j < f.cols - 1; j++) {
                grad_x.at<double>(i, j) = (f_double.at<double>(i, j + 1) - f_double.at<double>(i, j - 1)) / (2.0 * dx);
            }
        }
    }
    
    // 处理边界
    if (edge_order == 1) {
        // 一阶边缘差分 - 使用向量化操作
        cv::Mat first_row = f_double.row(1) - f_double.row(0);
        cv::Mat last_row = f_double.row(f.rows - 1) - f_double.row(f.rows - 2);
        first_row.copyTo(grad_y.row(0));
        last_row.copyTo(grad_y.row(f.rows - 1));
        grad_y.row(0) /= dx;
        grad_y.row(f.rows - 1) /= dx;
        
        cv::Mat first_col = f_double.col(1) - f_double.col(0);
        cv::Mat last_col = f_double.col(f.cols - 1) - f_double.col(f.cols - 2);
        first_col.copyTo(grad_x.col(0));
        last_col.copyTo(grad_x.col(f.cols - 1));
        grad_x.col(0) /= dx;
        grad_x.col(f.cols - 1) /= dx;
    } else {
        // 二阶边缘差分 - 使用矩阵操作
        double a_first = -1.5 / dx;
        double b_first = 2.0 / dx;
        double c_first = -0.5 / dx;
        
        double a_last = 0.5 / dx;
        double b_last = -2.0 / dx;
        double c_last = 1.5 / dx;
        
        // 垂直方向（y方向）边界
        if (f.rows >= 3) {
            // 第一行
            cv::Mat row0 = a_first * f_double.row(0) + b_first * f_double.row(1) + c_first * f_double.row(2);
            row0.copyTo(grad_y.row(0));
            
            // 最后一行
            cv::Mat rowLast = a_last * f_double.row(f.rows - 3) + b_last * f_double.row(f.rows - 2) + c_last * f_double.row(f.rows - 1);
            rowLast.copyTo(grad_y.row(f.rows - 1));
        } else {
            // 矩阵太小，使用原始方法
            for (int j = 0; j < f.cols; j++) {
                grad_y.at<double>(0, j) = a_first * f_double.at<double>(0, j) + b_first * f_double.at<double>(1, j);
                if (f.rows > 2) grad_y.at<double>(0, j) += c_first * f_double.at<double>(2, j);
                
                grad_y.at<double>(f.rows - 1, j) = c_last * f_double.at<double>(f.rows - 1, j) + b_last * f_double.at<double>(f.rows - 2, j);
                if (f.rows > 2) grad_y.at<double>(f.rows - 1, j) += a_last * f_double.at<double>(f.rows - 3, j);
            }
        }
        
        // 水平方向（x方向）边界
        if (f.cols >= 3) {
            // 第一列
            cv::Mat col0 = a_first * f_double.col(0) + b_first * f_double.col(1) + c_first * f_double.col(2);
            col0.copyTo(grad_x.col(0));
            
            // 最后一列
            cv::Mat colLast = a_last * f_double.col(f.cols - 3) + b_last * f_double.col(f.cols - 2) + c_last * f_double.col(f.cols - 1);
            colLast.copyTo(grad_x.col(f.cols - 1));
        } else {
            // 矩阵太小，使用原始方法
            for (int i = 0; i < f.rows; i++) {
                grad_x.at<double>(i, 0) = a_first * f_double.at<double>(i, 0) + b_first * f_double.at<double>(i, 1);
                if (f.cols > 2) grad_x.at<double>(i, 0) += c_first * f_double.at<double>(i, 2);
                
                grad_x.at<double>(i, f.cols - 1) = c_last * f_double.at<double>(i, f.cols - 1) + b_last * f_double.at<double>(i, f.cols - 2);
                if (f.cols > 2) grad_x.at<double>(i, f.cols - 1) += a_last * f_double.at<double>(i, f.cols - 3);
            }
        }
    }
    
    return std::make_pair(grad_y, grad_x);
}

/**
 * @brief 计算N维数组的梯度（多维版本）
 * 
 * 此函数计算多维数组的梯度，返回每个维度的梯度数组。
 * 使用OpenCV优化的矩阵操作提高效率。
 * 
 * @param f 包含标量函数样本的N维数组
 * @param edge_order 边界处梯度计算的精度阶数，可以是1或2，默认为1
 * @return 包含每个维度梯度的向量
 */
std::vector<cv::Mat> gradientND(const cv::Mat& f, int edge_order) {
    // 检查edge_order参数
    if (edge_order > 2) {
        throw std::invalid_argument("'edge_order' greater than 2 not supported");
    }
    
    // 获取维度数
    int dims = f.dims;
    if (dims < 2) dims = 2; // 至少处理为2D
    
    // 转换输入为double类型
    cv::Mat f_double;
    f.convertTo(f_double, CV_64F);
    
    std::vector<cv::Mat> outvals;
    
    // 使用统一间距为1.0
    double dx = 1.0;
    
    // 预先计算边缘差分系数
    double a_first = -1.5 / dx;
    double b_first = 2.0 / dx;
    double c_first = -0.5 / dx;
    
    double a_last = 0.5 / dx;
    double b_last = -2.0 / dx;
    double c_last = 1.5 / dx;
    
    // 对每个维度计算梯度
    for (int axis = 0; axis < dims; axis++) {
        // 创建结果数组
        cv::Mat out = cv::Mat::zeros(f.size(), CV_64F);
        
        if (axis == 0) {
            // 沿行方向的梯度（垂直方向）
            
            // 使用Sobel算子或自定义卷积核计算内部区域的梯度
            if (f.rows > 2) {
                // 提取内部区域（去除边界）
                cv::Rect interior_rect(0, 1, f.cols, f.rows - 2);
                cv::Mat interior = f_double(interior_rect);
                cv::Mat interior_out;
                
                // 使用垂直方向的Sobel算子
                cv::Mat kernel_y = (cv::Mat_<double>(3, 1) << -1, 0, 1) / (2.0 * dx);
                cv::filter2D(interior, interior_out, CV_64F, kernel_y);
                
                // 将计算结果复制到结果矩阵的内部区域
                interior_out.copyTo(out(interior_rect));
            }
            
            // 边缘处理 - 使用向量化操作
            if (edge_order == 1) {
                // 一阶边缘差分
                if (f.rows >= 2) {
                    cv::Mat first_row = (f_double.row(1) - f_double.row(0)) / dx;
                    first_row.copyTo(out.row(0));
                    
                    cv::Mat last_row = (f_double.row(f.rows - 1) - f_double.row(f.rows - 2)) / dx;
                    last_row.copyTo(out.row(f.rows - 1));
                }
            } else {
                // 二阶边缘差分
                if (f.rows >= 3) {
                    // 第一行 - 使用矩阵操作
                    cv::Mat row0 = a_first * f_double.row(0) + b_first * f_double.row(1) + c_first * f_double.row(2);
                    row0.copyTo(out.row(0));
                    
                    // 最后一行 - 使用矩阵操作
                    cv::Mat rowLast = a_last * f_double.row(f.rows - 3) + b_last * f_double.row(f.rows - 2) + c_last * f_double.row(f.rows - 1);
                    rowLast.copyTo(out.row(f.rows - 1));
                } else if (f.rows == 2) {
                    // 特殊情况：只有两行
                    cv::Mat row0 = (f_double.row(1) - f_double.row(0)) / dx;
                    row0.copyTo(out.row(0));
                    row0.copyTo(out.row(1));
                }
            }
        } else if (axis == 1) {
            // 沿列方向的梯度（水平方向）
            
            // 使用Sobel算子或自定义卷积核计算内部区域的梯度
            if (f.cols > 2) {
                // 提取内部区域（去除边界）
                cv::Rect interior_rect(1, 0, f.cols - 2, f.rows);
                cv::Mat interior = f_double(interior_rect);
                cv::Mat interior_out;
                
                // 使用水平方向的Sobel算子
                cv::Mat kernel_x = (cv::Mat_<double>(1, 3) << -1, 0, 1) / (2.0 * dx);
                cv::filter2D(interior, interior_out, CV_64F, kernel_x);
                
                // 将计算结果复制到结果矩阵的内部区域
                interior_out.copyTo(out(interior_rect));
            }
            
            // 边缘处理 - 使用向量化操作
            if (edge_order == 1) {
                // 一阶边缘差分
                if (f.cols >= 2) {
                    cv::Mat first_col = (f_double.col(1) - f_double.col(0)) / dx;
                    first_col.copyTo(out.col(0));
                    
                    cv::Mat last_col = (f_double.col(f.cols - 1) - f_double.col(f.cols - 2)) / dx;
                    last_col.copyTo(out.col(f.cols - 1));
                }
            } else {
                // 二阶边缘差分
                if (f.cols >= 3) {
                    // 第一列 - 使用矩阵操作
                    cv::Mat col0 = a_first * f_double.col(0) + b_first * f_double.col(1) + c_first * f_double.col(2);
                    col0.copyTo(out.col(0));
                    
                    // 最后一列 - 使用矩阵操作
                    cv::Mat colLast = a_last * f_double.col(f.cols - 3) + b_last * f_double.col(f.cols - 2) + c_last * f_double.col(f.cols - 1);
                    colLast.copyTo(out.col(f.cols - 1));
                } else if (f.cols == 2) {
                    // 特殊情况：只有两列
                    cv::Mat col0 = (f_double.col(1) - f_double.col(0)) / dx;
                    col0.copyTo(out.col(0));
                    col0.copyTo(out.col(1));
                }
            }
        }
        
        outvals.push_back(out);
    }
    
    return outvals;
}