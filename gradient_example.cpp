#include <iostream>
#include <opencv2/opencv.hpp>
#include <chrono>
#include "gradient.h"

// 用于性能测试的函数 - 原始版本
std::pair<cv::Mat, cv::Mat> gradient_original(const cv::Mat& f, int edge_order = 1) {
    // 检查edge_order参数
    if (edge_order > 2) {
        throw std::invalid_argument("'edge_order' greater than 2 not supported");
    }
    
    // 创建结果数组 - 垂直方向梯度（y方向）
    cv::Mat grad_y;
    f.convertTo(grad_y, CV_64F); // 确保输出是double类型
    grad_y = cv::Mat::zeros(f.size(), CV_64F);
    
    // 创建结果数组 - 水平方向梯度（x方向）
    cv::Mat grad_x;
    f.convertTo(grad_x, CV_64F); // 确保输出是double类型
    grad_x = cv::Mat::zeros(f.size(), CV_64F);
    
    // 使用统一间距为1.0
    double dx = 1.0;
    
    // 计算垂直方向梯度（y方向）
    // 内部点使用二阶中心差分
    for (int i = 1; i < f.rows - 1; i++) {
        for (int j = 0; j < f.cols; j++) {
            grad_y.at<double>(i, j) = (f.at<double>(i + 1, j) - f.at<double>(i - 1, j)) / (2.0 * dx);
        }
    }
    
    // 垂直方向边缘处理
    if (edge_order == 1) {
        // 一阶边缘差分
        for (int j = 0; j < f.cols; j++) {
            // 第一行
            grad_y.at<double>(0, j) = (f.at<double>(1, j) - f.at<double>(0, j)) / dx;
            // 最后一行
            grad_y.at<double>(f.rows - 1, j) = (f.at<double>(f.rows - 1, j) - f.at<double>(f.rows - 2, j)) / dx;
        }
    } else {
        // 二阶边缘差分
        for (int j = 0; j < f.cols; j++) {
            // 第一行
            double a = -1.5 / dx;
            double b = 2.0 / dx;
            double c = -0.5 / dx;
            grad_y.at<double>(0, j) = a * f.at<double>(0, j) + b * f.at<double>(1, j) + c * f.at<double>(2, j);
            
            // 最后一行
            a = 0.5 / dx;
            b = -2.0 / dx;
            c = 1.5 / dx;
            grad_y.at<double>(f.rows - 1, j) = a * f.at<double>(f.rows - 3, j) + 
                                              b * f.at<double>(f.rows - 2, j) + 
                                              c * f.at<double>(f.rows - 1, j);
        }
    }
    
    // 计算水平方向梯度（x方向）
    // 内部点使用二阶中心差分
    for (int i = 0; i < f.rows; i++) {
        for (int j = 1; j < f.cols - 1; j++) {
            grad_x.at<double>(i, j) = (f.at<double>(i, j + 1) - f.at<double>(i, j - 1)) / (2.0 * dx);
        }
    }
    
    // 水平方向边缘处理
    if (edge_order == 1) {
        // 一阶边缘差分
        for (int i = 0; i < f.rows; i++) {
            // 第一列
            grad_x.at<double>(i, 0) = (f.at<double>(i, 1) - f.at<double>(i, 0)) / dx;
            // 最后一列
            grad_x.at<double>(i, f.cols - 1) = (f.at<double>(i, f.cols - 1) - f.at<double>(i, f.cols - 2)) / dx;
        }
    } else {
        // 二阶边缘差分
        for (int i = 0; i < f.rows; i++) {
            // 第一列
            double a = -1.5 / dx;
            double b = 2.0 / dx;
            double c = -0.5 / dx;
            grad_x.at<double>(i, 0) = a * f.at<double>(i, 0) + b * f.at<double>(i, 1) + c * f.at<double>(i, 2);
            
            // 最后一列
            a = 0.5 / dx;
            b = -2.0 / dx;
            c = 1.5 / dx;
            grad_x.at<double>(i, f.cols - 1) = a * f.at<double>(i, f.cols - 3) + 
                                              b * f.at<double>(i, f.cols - 2) + 
                                              c * f.at<double>(i, f.cols - 1);
        }
    }
    
    return std::make_pair(grad_y, grad_x);
}

int main() {
    // 创建一个简单的测试矩阵
    cv::Mat f = (cv::Mat_<double>(6, 1) << 1, 2, 4, 7, 11, 16);
    std::cout << "输入矩阵:\n" << f << std::endl;
    
    // 创建一个二维测试矩阵
    cv::Mat f2d = (cv::Mat_<double>(2, 3) << 1, 2, 6, 3, 4, 5);
    std::cout << "\n二维输入矩阵:\n" << f2d << std::endl;
    
    // 使用修改后的gradient函数计算两个方向的梯度
    std::pair<cv::Mat, cv::Mat> grads = gradient(f2d);
    std::cout << "\n垂直方向梯度 (y方向, edge_order=1):\n" << grads.first << std::endl;
    std::cout << "\n水平方向梯度 (x方向, edge_order=1):\n" << grads.second << std::endl;
    
    // 使用edge_order=2计算梯度
    std::pair<cv::Mat, cv::Mat> grads2 = gradient(f2d, 2);
    std::cout << "\n垂直方向梯度 (y方向, edge_order=2):\n" << grads2.first << std::endl;
    std::cout << "\n水平方向梯度 (x方向, edge_order=2):\n" << grads2.second << std::endl;
    
    // 使用gradientND函数计算多维梯度
    std::vector<cv::Mat> gradsND = gradientND(f2d);
    std::cout << "\ngradientND - 行方向梯度 (axis=0):\n" << gradsND[0] << std::endl;
    std::cout << "\ngradientND - 列方向梯度 (axis=1):\n" << gradsND[1] << std::endl;
    
    // 计算梯度幅值（magnitude）
    cv::Mat magnitude;
    cv::magnitude(grads.first, grads.second, magnitude);
    std::cout << "\n梯度幅值:\n" << magnitude << std::endl;
    
    // 计算梯度方向（direction）
    cv::Mat direction;
    cv::phase(grads.second, grads.first, direction); // 注意参数顺序：x, y
    std::cout << "\n梯度方向 (弧度):\n" << direction << std::endl;
    
    // 性能测试
    std::cout << "\n性能测试" << std::endl;
    std::cout << "===================" << std::endl;
    
    // 创建一个较大的测试矩阵
    cv::Mat large_mat = cv::Mat::zeros(500, 500, CV_64F);
    cv::randu(large_mat, 0, 100); // 填充随机值
    
    // 测试原始版本性能
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 10; i++) {
        auto result = gradient_original(large_mat);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    
    std::cout << "原始版本 gradient 函数 (10次迭代): " << duration << " 毫秒" << std::endl;
    
    // 测试优化版本性能
    start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 10; i++) {
        auto result = gradient(large_mat);
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    
    std::cout << "优化版本 gradient 函数 (10次迭代): " << duration << " 毫秒" << std::endl;
    
    return 0;
}