#ifndef GRADIENT_H
#define GRADIENT_H

#include <opencv2/opencv.hpp>
#include <vector>

/**
 * @brief 计算N维数组的梯度，同时返回两个方向的梯度
 * 
 * 梯度使用二阶精度的中心差分计算内部点，在边界使用一阶或二阶精度的单侧差分（前向或后向）。
 * 返回的梯度与输入数组具有相同的形状。
 * 
 * @param f 包含标量函数样本的N维数组
 * @param edge_order 边界处梯度计算的精度阶数，可以是1或2，默认为1
 * @return 包含两个方向梯度的std::pair，first为垂直方向（y方向），second为水平方向（x方向）
 */
std::pair<cv::Mat, cv::Mat> gradient(const cv::Mat& f, int edge_order = 1);

/**
 * @brief 计算N维数组的梯度（多维版本）
 * 
 * 此函数计算多维数组的梯度，返回每个维度的梯度数组。
 * 
 * @param f 包含标量函数样本的N维数组
 * @param edge_order 边界处梯度计算的精度阶数，可以是1或2，默认为1
 * @return 包含每个维度梯度的向量
 */
std::vector<cv::Mat> gradientND(const cv::Mat& f, int edge_order = 1);

#endif // GRADIENT_H