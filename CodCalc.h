﻿#pragma once

#include <opencv2/opencv.hpp>
#include <cmath>
#include <numeric>
#include <algorithm>
#include <Eigen/Dense>
#include <unsupported/Eigen/NonLinearOptimization>
#include <unsupported/Eigen/NumericalDiff>


using  cv::Mat;

class CodCalc
{

public:

	Mat makeWindow(int height, int width, double sigma = 10.0);

	// FFT 乘窗 + 逆变换
	Mat applyFFTFilter(const cv::Mat& mat, double sigma, int pad = 100, int borderType = cv::BORDER_REFLECT_101);

	cv::Mat normalization_fft(const cv::Mat& mat, double sigma = 10.0, int pad = 100, int borderType = cv::BORDER_REFLECT_101);


	cv::Mat convert_chessboard_to_linepattern(const cv::Mat& mat, bool smooth = true,
		bool bright = true, int sigma = 3);

	cv::Mat select_roi(const cv::Mat& mat, float ratio, bool square = false);

	cv::Mat make_circle_mask(int width, float ratio);

	// ---------------- 高斯函数 ----------------
	inline static double gauss_function(double x, double a, double b, double c, double d) {
		double diff = x - c;
		return a * std::exp(-diff * diff / (2.0 * b * b)) + d;
	}

	// ---------------- 高斯拟合 ----------------
	void get_gauss_peak_fit(const std::vector<double>& list_data, std::vector<double>& fit_data,
		double& peak_c, double& offset, bool& check);


	// ---------------- 子像素极值定位 ----------------
	double locate_subpixel_point(const std::vector<double>& list_point, bool bMin = true);


	double calcStd(const std::vector<double>& list_data);


	// ---------------- 选择好的极值点 ----------------
	std::vector<int> select_good_peaks(std::vector<double> list_data, const std::vector<int>& peaks,
		double tol = 0.2, int radius = 11, double sigma = 0, bool use_offset = true);

	std::vector<double> get_local_extrema_points(
		const std::vector<double>& list_data_in, bool bMin = true, int radius = 7, double sensitive = 0.1,
		bool denoise = true, bool norm = true, bool subpixel = true, bool select_peaks_flag = false);

	static std::vector<double> genAngle(double start, double stop_inclusive, double step);

	// =============================
// Radon Transform
// =============================
	//theta:  投影角度（单位：度）
	//circle:  是否使用内接圆裁剪
	//preserve_range: 是否保留原始范围（这里等效）
	cv::Mat radon(const cv::Mat& image, const std::vector<double>& theta = {}, bool circle = true, bool preserve_range = false);            

	// 计算某一维度的梯度
	cv::Mat gradient_along_axis(const cv::Mat& src, int axis);

	std::pair<double, double> calc_slope_distance_hor_lines(
		const cv::Mat& mat_in, bool chessboard = false, double ratio = 0.3, double search_range = 30.0,int radius = 9,
		double sensitive = 0.1,bool bright = true,bool denoise = true,bool norm = true,
		bool subpixel = true,bool select_peaks = false);

	std::pair<double, double> calc_slope_distance_ver_lines(
		const cv::Mat& mat_in, bool chessboard = false, double ratio = 0.3, double search_range = 30.0, int radius = 9, double sensitive = 0.1,
		bool bright = true, bool denoise = true, bool norm = true, bool subpixel = true,  bool select_peaks = false);

	std::pair<int, int> calc_index_range(int height, int width, double angle_deg, bool isHorizontal);

	std::vector<double> sliding_window_slope(const std::vector<double>& list_data, int size = 3, bool norm = true);

	// --- 双三次插值 (Eigen 版) ---
	double cubicInterpolate(const cv::Mat& img, double y, double x);

	// get_tilted_profile
	std::tuple<std::vector<double>, std::vector<double>, std::vector<double>>
		get_tilted_profile(const cv::Mat& mat, int index, double angle_deg, bool isHorizontal);

	std::vector<cv::Point2d> get_cross_points_hor_lines(
		const cv::Mat& mat_in, double slope_ver, double dist_ver, bool chessboard = false, int radius = 11, 
		double sensitive = 0.1, double ratio = 0.3, bool norm = true, int offset = 0, bool bright = true,
		bool denoise = true, bool subpixel = true, bool select_peaks = false);

	// ---------------- 主函数转换 ----------------
	std::vector<cv::Point2d> get_cross_points_ver_lines(
		const cv::Mat& mat_in, double slope_hor, double dist_hor, bool chessboard = false, int radius = 11,
		double sensitive = 0.1, double ratio = 0.3, bool norm = true, int offset = 0, bool bright = true,
		bool denoise = true, bool subpixel = true, bool select_peaks = false);

	// 生成带抛物线边界的 mask
	cv::Mat make_parabola_mask(int height, int width,double hor_curviness = 0.3, double ver_curviness = 0.3,
		std::pair<int, int> hor_margin = { 100,100 }, std::pair<int, int> ver_margin = { 100,100 }, double rotate = 0.0);


	std::vector<cv::Point2d> remove_points_using_parabola_mask(const std::vector<cv::Point2d>& points,
		int height, int width, double hor_curviness = 0.3, double ver_curviness = 0.3,
		std::pair<int, int> hor_margin = { 100, 100 }, std::pair<int, int> ver_margin = { 100, 100 }, double rotate = 0.0);

	// 输入输出都是 (x, y) 坐标
	std::vector<cv::Point2d> rotate_points(const std::vector<cv::Point2d>& points, double angle, bool degree_unit = true);

	bool check_dot_on_line_hor(
		const cv::Point2d& dot1, const cv::Point2d& dot2, double slope, double dot_dist, double ratio, int num_dot_miss);


	bool check_dot_on_line_ver(
		const cv::Point2d& dot1, const cv::Point2d& dot2, double slope, double dot_dist, double ratio, int num_dot_miss);


	// group_dots_hor_lines
	std::vector<std::vector<cv::Point2d>> group_dots_hor_lines(const std::vector<cv::Point2d>& list_dots, 
		double slope, double dot_dist, double ratio = 0.3, int num_dot_miss = 6, double accepted_ratio = 0.65);

	// 拟合多项式: y = a0 + a1*x + a2*x^2 + ... + an*x^n
	Eigen::VectorXd polyfit(const std::vector<double>& x, const std::vector<double>& y, int order);

	// 计算多项式在 x 上的值
	double polyval(const Eigen::VectorXd& coeff, double x);

	// 获取邻近点
	std::vector<cv::Point2d> get_nearby_hor_points(const std::vector<cv::Point2d>& current_points,
		const std::vector<cv::Point2d>& points, double residual, int order = 2);

	// 工具: 去重 (按 x,y)
	static void unique_points(std::vector<cv::Point2d>& points);

	std::vector<cv::Point2d> get_nearby_hor_points_iter(
		const std::vector<cv::Point2d>& initial_points, const std::vector<cv::Point2d>& points, double x_left,
		double x_right, double search_dist, double residual, double overlap_ratio = 0.5, int order = 2);

	std::vector<std::vector<cv::Point2d>> group_dots_hor_lines_based_polyfit(
		const std::vector<cv::Point2d>& points,
		double slope, double line_dist,
		double ratio = 0.1, int num_dot_miss = 3, double accepted_ratio = 0.65,
		double overlap_ratio = 0.5, int order = 2);

	std::vector<std::vector<cv::Point2d>> group_dots_ver_lines(
		const std::vector<cv::Point2d>& list_dots, double slope, double dot_dist,
		double ratio = 0.3, int num_dot_miss = 6, double accepted_ratio = 0.75);

	std::vector<cv::Point2d> get_nearby_ver_points(const std::vector<cv::Point2d>& current_points,
		const std::vector<cv::Point2d>& points, double residual, int order = 2);

	std::vector<cv::Point2d> get_nearby_ver_points_iter(
		const std::vector<cv::Point2d>& initial_points,	const std::vector<cv::Point2d>& points,
		double y_left, double y_right, double search_dist, double residual, double overlap_ratio = 0.5,int order = 2);


	std::vector<std::vector<cv::Point2d>> group_dots_ver_lines_based_polyfit(
		const std::vector<cv::Point2d>& points,
		double slope, double line_dist,
		double ratio = 0.1, int num_dot_miss = 3,
		double accepted_ratio = 0.65, double overlap_ratio = 0.5, int order = 2);


	// 拟合并去掉残差大的点
	std::vector<std::vector<cv::Point2d>> remove_residual_dots_hor(
		const std::vector<std::vector<cv::Point2d>>& list_lines, double slope, double residual = 2.5);

	std::vector<std::vector<cv::Point2d>> remove_residual_dots_ver(
		const std::vector<std::vector<cv::Point2d>>& list_lines,
		double slope, double residual = 2.5);



	std::pair<std::vector<Eigen::VectorXd>, std::vector<std::vector<cv::Point2d>>>
		_para_fit_hor(const std::vector<std::vector<cv::Point2d>>& list_lines, double xcenter, double ycenter);

	std::pair<std::vector<Eigen::VectorXd>, std::vector<std::vector<cv::Point2d>>>
		_para_fit_ver(const std::vector<std::vector<cv::Point2d>>& list_lines, double xcenter, double ycenter);

	double median(std::vector<double> vec);

	std::pair<double, double>
		__get_representative_linear_coefs(const std::vector<Eigen::VectorXd>& list_coefs,const std::vector<int>& indices,
			const std::string& method);

	std::pair<double, double> _find_cross_point_between_lines(
		const Eigen::Vector2d& line_coef_hor,  // [a1, b1]  y = a1*x + b1
		const Eigen::Vector2d& line_coef_ver);  // [a2, b2]  x = a2*y + b2


	std::pair<std::vector<cv::Point2d>, std::vector<cv::Point2d>>
		generate_4_source_target_perspective_points(
			const std::vector<cv::Point2d>& points_in,
			bool equal_dist = false,
			const std::string& scale_mode = "mean",
			double scale_value = 1.0);   // 仅当 scale_mode 不是 "mean/min/max" 时才用


	std::vector<double> _find_cross_point_between_parabolas_same_direction(const Eigen::Vector3d& para_coef1,
			const Eigen::Vector3d& para_coef2);

	std::pair<double, double>
		find_cod_coarse(const std::vector<std::vector<cv::Point2d>>& list_hor_lines,
			const std::vector<std::vector<cv::Point2d>>& list_ver_lines);


	std::pair<double, double>find_cod_bailey(const std::vector<std::vector<cv::Point2d>>& list_hor_lines,
			const std::vector<std::vector<cv::Point2d>>& list_ver_lines, int iteration = 2);

	std::pair<double, double> _find_center_based_vanishing_points_2nd_way(
			const std::vector<std::vector<cv::Point2d>>& list_hor_lines_in,
			const std::vector<std::vector<cv::Point2d>>& list_ver_lines_in);

	Eigen::VectorXd calc_perspective_coefficients(
		const std::vector<cv::Point2d>& source_points,
		const std::vector<cv::Point2d>& target_points,
		const std::string& mapping = "backward");

	std::vector<std::vector<cv::Point2d>> correct_perspective_line(
		const std::vector<std::vector<cv::Point2d>>& list_lines,
		const Eigen::VectorXd& list_coef);

	std::vector<std::vector<cv::Point2d>> update_center(
		const std::vector<std::vector<cv::Point2d>>& list_lines,
		double xcenter,
		double ycenter);


	std::pair<std::vector<std::vector<cv::Point2d>>, std::vector<std::vector<cv::Point2d>>>
		correct_perspective_effect(
			const std::vector<std::vector<cv::Point2d>>& list_hor_lines_in,
			const std::vector<std::vector<cv::Point2d>>& list_ver_lines_in,
			double xcenter, double ycenter,
			const std::string& method = "mean",
			const std::string& scaleMode = "mean");

	std::pair<double, double> find_center_based_vanishing_points_iteration(
		const std::vector<std::vector<cv::Point2d>>& list_hor_lines,
		const std::vector<std::vector<cv::Point2d>>& list_ver_lines,
		int iteration = 2,
		const std::string& method = "mean");

};

