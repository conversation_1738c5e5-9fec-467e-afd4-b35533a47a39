﻿#pragma once

#include <opencv2/opencv.hpp>
#include <cmath>
#include <numeric>
#include <algorithm>
#include <Eigen/Dense>
#include <unsupported/Eigen/NonLinearOptimization>
#include <unsupported/Eigen/NumericalDiff>


using  cv::Mat;

class CodCalc
{

public:

	Mat makeWindow(int height, int width, double sigma = 10.0);

	// FFT 乘窗 + 逆变换
	Mat applyFFTFilter(const cv::Mat& mat, double sigma, int pad = 100, int borderType = cv::BORDER_REFLECT_101);

	cv::Mat normalization_fft(const cv::Mat& mat, double sigma = 10.0, int pad = 100, int borderType = cv::BORDER_REFLECT_101);


	cv::Mat convert_chessboard_to_linepattern(const cv::Mat& mat, bool smooth = true,
		bool bright = true, int sigma = 3);

	cv::Mat select_roi(const cv::Mat& mat, float ratio, bool square = false);

	cv::Mat make_circle_mask(int width, float ratio);

	// ---------------- 高斯函数 ----------------
	inline static double gauss_function(double x, double a, double b, double c, double d) {
		double diff = x - c;
		return a * std::exp(-diff * diff / (2.0 * b * b)) + d;
	}

	// ---------------- 高斯拟合 ----------------
	bool get_gauss_peak_fit(const std::vector<double>& list_data,
		std::vector<double>& fit_data,
		double& peak_c,
		double& offset,
		bool& check);



	// ---------------- 子像素极值定位 ----------------
	double locate_subpixel_point(const std::vector<double>& list_point, const std::string& option = "min")
	{
		int n = list_point.size();
		Eigen::VectorXd x(n), y(n);
		for (int i = 0; i < n; i++) { x(i) = i; y(i) = list_point[i]; }

		Eigen::MatrixXd A(n, 3);
		for (int i = 0; i < n; i++) {
			A(i, 0) = x(i) * x(i);
			A(i, 1) = x(i);
			A(i, 2) = 1.0;
		}
		Eigen::VectorXd coeff = A.colPivHouseholderQr().solve(y);
		double a = coeff(0), b = coeff(1);

		int pos = (option == "min") ?
			(int)(std::min_element(list_point.begin(), list_point.end()) - list_point.begin()) :
			(int)(std::max_element(list_point.begin(), list_point.end()) - list_point.begin());

		if (a != 0.0) {
			double num = -b / (2 * a);
			if (num >= 0 && num < n) pos = num;
		}
		return pos;
	}




};

