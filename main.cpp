﻿//#include"cfg.h"


//#include "LensDetect.h"
//#include "LensDetect2.h"

#include <fstream>
#include "CodCalc.h"

#include "libcbdetect/boards_from_corners.h"
#include "libcbdetect/config.h"
#include "libcbdetect/find_corners.h"
#include "libcbdetect/plot_boards.h"
#include "libcbdetect/plot_corners.h"


using namespace cv;
using cv::Point;
using std::vector;

using std::string;
using std::cout;
using std::endl;
using std::ios;

//#define  ORIGIN_METHOD

int loadRawData(unsigned short* pDst, int nWidth, int nHeight, const string& imgPath)
{
	std::ifstream ifs(imgPath, ios::binary);
	if (!ifs.is_open())
	{
		cout << "open file failed" << endl;
		return -1;
	}



	int fileSize = nWidth * nHeight * 2;

	ifs.read((char*)pDst, fileSize);

	if (ifs.gcount() != fileSize)
	{
		return -2;
	}

	return 0;
}

int raw10ToRaw8(unsigned short* pInRaw, unsigned char* pOutRaw,
	const int& nWidth, const int& nHeight)
{

	const int imSize = nWidth * nHeight;
	for (int idx = 0; idx < imSize; idx++)
	{
		pOutRaw[idx] = (pInRaw[idx] >> 2) & 0xff;
	}
	return 0;
}


float getAngle(const cv::Point2f& a, const cv::Point2f& b, const cv::Point2f& c, cv::Point2f& d)
{
	auto angle1 = atan2(b.y - a.y, b.x - a.x);
	auto angle2 = atan2(d.y - c.y, d.x - c.x);
	auto dstAngle = static_cast<float>(abs((angle1 - angle2) * 180.f / CV_PI));
	return dstAngle;
}

// 计算类似 numpy.gradient 的二维版本
cv::Mat gradient(const cv::Mat& mat, int axis, int edge_order = 1) {
	if (mat.empty())
		throw std::invalid_argument("Input matrix is empty");
	if (mat.type() != CV_64F)
		throw std::invalid_argument("Input must be CV_64F (double)");
	if (mat.rows < 2 && axis == 0)
		throw std::invalid_argument("Too few rows for gradient along axis=0");
	if (mat.cols < 2 && axis == 1)
		throw std::invalid_argument("Too few cols for gradient along axis=1");

	cv::Mat grad = cv::Mat::zeros(mat.size(), CV_64F);

	if (axis == 0) {
		// ---- 沿行方向 (y 方向) ----
		for (int j = 0; j < mat.cols; ++j) {
			// 前向差分 (第一行)
			grad.at<double>(0, j) = mat.at<double>(1, j) - mat.at<double>(0, j);

			// 中心差分 (中间行)
			for (int i = 1; i < mat.rows - 1; ++i) {
				grad.at<double>(i, j) = (mat.at<double>(i + 1, j) - mat.at<double>(i - 1, j)) / 2.0;
			}

			// 后向差分 (最后一行)
			grad.at<double>(mat.rows - 1, j) = mat.at<double>(mat.rows - 1, j) - mat.at<double>(mat.rows - 2, j);
		}
	}
	else if (axis == 1) {
		// ---- 沿列方向 (x 方向) ----
		for (int i = 0; i < mat.rows; ++i) {
			// 前向差分 (第一列)
			grad.at<double>(i, 0) = mat.at<double>(i, 1) - mat.at<double>(i, 0);

			// 中心差分 (中间列)
			for (int j = 1; j < mat.cols - 1; ++j) {
				grad.at<double>(i, j) = (mat.at<double>(i, j + 1) - mat.at<double>(i, j - 1)) / 2.0;
			}

			// 后向差分 (最后一列)
			grad.at<double>(i, mat.cols - 1) = mat.at<double>(i, mat.cols - 1) - mat.at<double>(i, mat.cols - 2);
		}
	}
	else {
		throw std::invalid_argument("Axis must be 0 (rows) or 1 (cols)");
	}

	return grad;
}

cv::Mat loadTxt(const std::string& filename, int rows, int cols)
{
	cv::Mat m(rows, cols, CV_64F);
	std::ifstream file(filename);
	for (int r = 0; r < rows; r++) {
		for (int c = 0; c < cols; c++) {
			file >> m.at<double>(r, c);
		}
	}
	return m;
}

bool compareMat(const cv::Mat& a, const cv::Mat& b, double tol = 1e-6)
{
	if (a.size != b.size || a.type() != b.type()) return false;
	cv::Mat diff;
	cv::absdiff(a, b, diff);
	return cv::countNonZero(diff > tol) == 0;
}

void testCodCalc()
{


	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\图片\\2AV_ChessBoard\\4E4C4E322D173E0CT\\_8_gray.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\1\\0302040000008557F0019377SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\2\\0302040000008557F0012969SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\3\\0302040000008557F0013376SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\4\\0302040000008557F0011589SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\5\\0302040000008557F0020154SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\6\\0302040000008557F0012513SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\7\\0302040000008557F0011097SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\8\\0302040000008557F0012356SJ7_8.bmp";
	//cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\3AV标定图片+数据\\9\\0302040000008557F0011122SJ7_8.bmp";
	cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\Cam0__2025-09-22_11.01.54[1600x1200][BGGR][BIT10].raw";
	
	const int nWidth = 1600;
    const int nHeight = 1200;
	unsigned short* pRawData = new unsigned short[nWidth * nHeight];
	loadRawData(pRawData, nWidth, nHeight, imgPath);

	unsigned char* pRaw8Data = new unsigned char[nWidth * nHeight];
	raw10ToRaw8(pRawData, pRaw8Data, nWidth, nHeight);

	Mat rawImg = Mat(nHeight, nWidth, CV_8UC1, pRaw8Data);

	Mat imgRgb = Mat(nHeight, nWidth, CV_8UC3);
	Mat imgRgb2 = Mat(nHeight, nWidth, CV_8UC3);
	Mat grayImg;
	Mat grayImg2;
	cvtColor(rawImg, imgRgb, COLOR_BayerBG2BGR_EA);
	cvtColor(rawImg, imgRgb2, COLOR_BayerBG2BGR_VNG);
	cvtColor(imgRgb, grayImg, COLOR_RGB2GRAY);
	cvtColor(imgRgb2, grayImg2, COLOR_RGB2GRAY);
	imwrite("E:\\test0900.bmp", imgRgb);

	Mat imgSrc = grayImg.clone();
//	Mat imgSrc = imread(imgPath, IMREAD_GRAYSCALE);
// Mat grayImg = imgSrc.clone();

	imgSrc.convertTo(imgSrc, CV_64FC1);


	CodCalc codCalc;

#ifdef ORIGIN_METHOD
	auto imNormalized = codCalc.normalization_fft(imgSrc, 10.0);
	auto slope_distH = codCalc.calc_slope_distance_hor_lines(imNormalized, true);
	auto slope_distV = codCalc.calc_slope_distance_ver_lines(imNormalized, true);

	auto slope_hor = slope_distH.first;
	auto dist_hor = slope_distH.second;
	auto slope_ver = slope_distV.first;
	auto dist_ver = slope_distV.second;

	auto list_points_hor_lines = codCalc.get_cross_points_hor_lines(imNormalized, slope_ver, dist_ver, true, 9, 0.3);
	auto list_points_ver_lines = codCalc.get_cross_points_ver_lines(imNormalized, slope_hor, dist_hor, true, 9, 0.3);

	auto hor_margin = std::make_pair<int, int>(500, 450);
	auto ver_margin = std::make_pair<int, int>(250, 250);

	list_points_hor_lines = codCalc.remove_points_using_parabola_mask(list_points_hor_lines, nHeight, nWidth, 0.4, 0.3, hor_margin, ver_margin);
	list_points_ver_lines = codCalc.remove_points_using_parabola_mask(list_points_ver_lines, nHeight, nWidth, 0.4, 0.3, hor_margin, ver_margin);


	auto list_hor_lines = codCalc.group_dots_hor_lines_based_polyfit(list_points_hor_lines, slope_hor, dist_hor);
	auto list_ver_lines = codCalc.group_dots_ver_lines_based_polyfit(list_points_ver_lines, slope_ver, dist_ver);

	list_hor_lines = codCalc.remove_residual_dots_hor(list_hor_lines, slope_hor, 3.0);
	list_ver_lines = codCalc.remove_residual_dots_ver(list_ver_lines, slope_ver, 3.0);

	for (auto& line : list_hor_lines)
	{
		for (int i = 1; i < line.size(); i++)
		{
			cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(255), 1);
		}

	}

	for (auto& line : list_ver_lines)
	{
		for (int i = 1; i < line.size(); i++)
		{
			cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(128), 1);
		}

	}


	auto center = codCalc.find_center_based_vanishing_points_iteration(list_hor_lines, list_ver_lines, 2);

	std::cout << "Center: " << center.first << "," << center.second << std::endl;
#endif
	//int pointNumH = 32;
	//int pointNumV = 21;

	//int flag{ 0 };
	//flag |= CALIB_CB_NORMALIZE_IMAGE;
	//flag |= CALIB_CB_ADAPTIVE_THRESH;
	////flag |= CALIB_CB_FAST_CHECK;
 //   //flag |= CALIB_CB_FILTER_QUADS;

	//std::vector<Point2f> corners;

	//bool isFound = findChessboardCorners(grayImg, Size(pointNumH, pointNumV), corners, flag);

	//int nWndSize = 7;
	//nWndSize = nWndSize + max(nWndSize % 2, 1);
	//Size sizeWnd{ nWndSize, nWndSize };

	//TermCriteria criteria = TermCriteria(
	//	TermCriteria::EPS + TermCriteria::MAX_ITER,
	//	60, 0.000001f);

	//cornerSubPix(grayImg, corners, sizeWnd, Size(-1, -1), criteria);

#ifndef ORIGIN_METHOD
	std::vector<std::vector<Point2d>>	list_hor_lines;
	std::vector<std::vector<Point2d>>	 list_ver_lines;
#endif
	//for (int i = 0; i < corners.size(); i += pointNumH)
	//{
	//	std::vector<Point2d> line;
	//	for (int j = 0; j < pointNumH; j++)
	//	{
	//		line.push_back(corners[i + j]);
	//	}
	//	list_hor_lines.push_back(line);
	//}


	//int index =0;
	//for (int i = 0; i < pointNumV; i++)
	//{
	//	for (int j = 0; j < pointNumH; j++)
	//	{

	//		if (i == 0)
	//		{
	//			std::vector<Point2d> line;
	//			line.push_back(corners[index]);
	//			list_ver_lines.push_back(line);
	//		}
	//		else
	//		{
	//			list_ver_lines[j].push_back(corners[index]);
	//		}
 //           index++;
	//	}

	//}

	//for (auto& line : list_hor_lines)
	//{
	//	for (int i = 1; i < line.size(); i++)
	//	{
	//		cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(255), 1);
	//	}

	//}

	//for (auto& line : list_ver_lines)
	//{
	//	for (int i = 1; i < line.size(); i++)
	//	{
	//		cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(128), 1);
	//	}

	//}

	cbdetect::Corner corners;
	std::vector<cbdetect::Board> boards;

	cbdetect::Params params;
	params.score_thr = 0.005;
	params.init_loc_thr = 0.002;
	params.corner_type = cbdetect::SaddlePoint;
	params.norm = true;
	params.norm_half_kernel_size = 7;
	params.show_debug_image = false;

	cvtColor(imgRgb, imgRgb, COLOR_RGB2BGR);

	cbdetect::find_corners(imgRgb, corners, params);

	std::vector<Point2f> corners1;
	for (auto pt : corners.p)
	{
		corners1.push_back(Point2f(pt));
	}

	int nWndSize = 7;
	nWndSize = nWndSize + max(nWndSize % 2, 1);
	Size sizeWnd{ nWndSize, nWndSize };

	TermCriteria criteria = TermCriteria(
		TermCriteria::EPS + TermCriteria::MAX_ITER,
		100, 0.000001f);

	cornerSubPix(grayImg, corners1, sizeWnd, Size(-1, -1), criteria);

	for (int i = 0; i < corners1.size(); i++)
	{
		corners.p[i] = Point2d(corners1[i]);

		drawMarker(imgRgb, corners1[i], Scalar::all(255), MARKER_TILTED_CROSS, 20);
	}


    cbdetect::boards_from_corners(imgRgb, corners, boards, params);

	int offset = 0;

	std::vector<std::vector<Point2d>> list_hor_lines2;
	std::vector<std::vector<Point2d>> list_ver_lines2;

	const int nCornerHCnt = 33;
    const int nCornerVCnt = 24;

	bool isSwitchVH = false;

	for (auto& board : boards)
	{
		list_ver_lines.clear();
		list_hor_lines.clear();

		isSwitchVH = (board.idx.size() - 2 == nCornerHCnt);

		std::vector<bool> list_hor_keep;
		std::vector<bool> list_ver_keep;
		list_hor_keep.resize(isSwitchVH ? nCornerHCnt : nCornerVCnt);
		list_ver_keep.resize(isSwitchVH ? nCornerVCnt : nCornerHCnt);

		list_hor_lines.resize(isSwitchVH ? nCornerHCnt : nCornerVCnt);
		list_ver_lines.resize(isSwitchVH ? nCornerVCnt : nCornerHCnt);

		for (auto& item : list_ver_keep)
		{
			item = true;
		}

		for (auto& item : list_hor_keep)
		{
			item = true;
		}

		for (int i = 1; i < board.idx.size() - 1; ++i)
		{
			std::vector<Point2d> lineH;
			for (int j = 1; j < board.idx[i].size() - 1; ++j)
			{
				if (board.idx[i][j] < 0)
				{
					list_ver_keep[j - 1] = false;
					list_hor_keep[i - 1] = false;
					continue;
				}

				auto pt = corners.p[board.idx[i][j]];
				lineH.push_back(pt);
				list_ver_lines[j - 1].push_back(pt);

				//#ifdef _DEBUG
				//				  circle(imgBgr, pt, 7, Scalar::all(255), 1);
				//				  drawMarker(imgBgr, pt, Scalar(255, 0, 0), MARKER_TILTED_CROSS, 7);
				//#endif

			}
			list_hor_lines[i - 1] = lineH;
		}

		for (int i = 0; i < list_hor_keep.size(); i++)
		{
			if (list_hor_keep[i])
			{
				list_hor_lines2.push_back(list_hor_lines[i]);
			}
		}

		for (int i = 0; i < list_ver_keep.size(); i++)
		{
			if (list_ver_keep[i])
			{
				list_ver_lines2.push_back(list_ver_lines[i]);
			}
		}



	}


	for (auto& line : list_hor_lines)
	{
		for (int i = 1; i < line.size(); i++)
		{
			cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(255), 1);
		}

	}

	for (auto& line : list_ver_lines)
	{
		for (int i = 1; i < line.size(); i++)
		{
			cv::line(imgSrc, line[i - 1], line[i], cv::Scalar::all(255), 1);
		}

	}



    auto center2 = codCalc.find_center_based_vanishing_points_iteration(list_hor_lines, list_ver_lines, 4);
	std::cout << "Center2: " << center2.first << "," << center2.second << std::endl;

}






int main(int argc, char const* argv[])
{
	//testTilt();

	testCodCalc();

	//return blemishForToF();

	//flareTest();

	//return	lensDetectTest();

	return 0;
}





