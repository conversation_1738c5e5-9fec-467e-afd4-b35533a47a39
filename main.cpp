﻿#define _CRT_SECURE_NO_WARNINGS
//#include"cfg.h"

#pragma comment(lib, "opencv_world320d.lib")

//#include "LensDetect.h"
//#include "LensDetect2.h"

#include <fstream>
#include "CodCalc.h"

using namespace cv;

//void test_getdeep();
//
//void load_image_points(vector< vector< Point2d > >& left_img_points, vector< vector< Point2d > >& right_img_points, vector< vector< Point3d > >& object_points) {
//	printf("Finding chessboard corners...\n");
//
//	vector< vector< Point2f > > imagePoints1, imagePoints2;
//	vector< Point2f > corners1, corners2;
//	Size board_size = Size(board_width, board_height);
//	int board_n = board_width * board_height;
//
//	// findChessboardCorners in each img 
//	for (int i = 1; i <= num_imgs; i++) {
//		char left_img[100], right_img[100];
//		sprintf(left_img, "%s%d%s", img_dir, i, leftimg_postfix);
//		sprintf(right_img, "%s%d%s", img_dir, i, rightimg_postfix);
//		Mat img1 = imread(left_img, CV_LOAD_IMAGE_COLOR);
//		Mat img2 = imread(right_img, CV_LOAD_IMAGE_COLOR);
//
//		bool found1 = false, found2 = false;
//
//		found1 = cv::findChessboardCorners(img1, board_size, corners1,
//			CV_CALIB_CB_ADAPTIVE_THRESH | CV_CALIB_CB_FILTER_QUADS);
//		found2 = cv::findChessboardCorners(img2, board_size, corners2,
//			CV_CALIB_CB_ADAPTIVE_THRESH | CV_CALIB_CB_FILTER_QUADS);
//		/* show the corners in img
//		Mat gray1, gray2;
//		cv::cvtColor(img1, gray1, CV_BGR2GRAY);
//		cv::cvtColor(img2, gray2, CV_BGR2GRAY);
//		if (found1)
//		{
//			cv::cornerSubPix(gray1, corners1, cv::Size(5, 5), cv::Size(-1, -1),
//				cv::TermCriteria(CV_TERMCRIT_EPS | CV_TERMCRIT_ITER, 30, 0.1));
//			cv::drawChessboardCorners(gray1, board_size, corners1, found1);
//		}
//		if (found2)
//		{
//			cv::cornerSubPix(gray2, corners2, cv::Size(5, 5), cv::Size(-1, -1),
//				cv::TermCriteria(CV_TERMCRIT_EPS | CV_TERMCRIT_ITER, 30, 0.1));
//			cv::drawChessboardCorners(gray2, board_size, corners2, found2);
//		}*/
//
//		vector<cv::Point3d> obj;
//		for (int i = 0; i < board_height; ++i)
//			for (int j = 0; j < board_width; ++j)
//				obj.push_back(Point3d(double((float)j * square_size), double((float)i * square_size), 0));
//		// find corners in both left_img and right_img. save the corners in imagePoints
//		if (found1 && found2) {
//			cout << i << ". found corners successfully." << endl;
//			imagePoints1.push_back(corners1);
//			imagePoints2.push_back(corners2);
//			object_points.push_back(obj);
//		}
//		else {
//			cout << i << ". failed to find corners, you can delete it." << endl;
//		}
//	}
//	cout << endl;
//	//save the message of corners as vector< vector< Point2d > >
//	for (int i = 0; i < imagePoints1.size(); i++) {
//		vector< Point2d> v1, v2;
//		for (int j = 0; j < imagePoints1[i].size(); j++) {
//			v1.push_back(Point2d((double)imagePoints1[i][j].x, (double)imagePoints1[i][j].y));
//			v2.push_back(Point2d((double)imagePoints2[i][j].x, (double)imagePoints2[i][j].y));
//		}
//		left_img_points.push_back(v1);
//		right_img_points.push_back(v2);
//	}
//}
//
//
//void calibration(FileStorage fs, vector< vector< Point2d > > left_img_points, vector< vector< Point2d > > right_img_points, vector< vector< Point3d > > object_points,
//	Matx33d& K1, Vec4d& D1, Matx33d& K2, Vec4d& D2, Matx33d& R, Vec3d& T) {
//	printf("Calibrating...\n\n");
//	int flag = 0;
//	flag |= cv::fisheye::CALIB_RECOMPUTE_EXTRINSIC;
//	flag |= cv::fisheye::CALIB_CHECK_COND;
//	flag |= cv::fisheye::CALIB_FIX_SKEW;
//	//flag |= cv::fisheye::CALIB_FIX_K2;
//	//flag |= cv::fisheye::CALIB_FIX_K3;
//	//flag |= cv::fisheye::CALIB_FIX_K4;
//	cv::fisheye::stereoCalibrate(object_points, left_img_points, right_img_points,
//		K1, D1, K2, D2, Size(w, h), R, T, flag,
//		cv::TermCriteria(3, 12, 0));
//
//	fs << "K1" << Mat(K1);
//	fs << "K2" << Mat(K2);
//	fs << "D1" << D1;
//	fs << "D2" << D2;
//	fs << "R" << Mat(R);
//	fs << "T" << T;
//}
//
//
//void stereo(FileStorage fs, Matx33d K1, Vec4d D1, Matx33d K2, Vec4d D2, Matx33d R, Vec3d T) {
//	printf("Stereorectifing...\n\n");
//
//	// stereorectify related
//	cv::Mat R1, R2, P1, P2, Q;
//	cv::fisheye::stereoRectify(K1, D1, K2, D2, Size(w,h), R, T, R1, R2, P1, P2,
//		Q, CV_CALIB_ZERO_DISPARITY, Size(w, h), 0.0, 1.1);
//
//	fs << "R1" << R1;
//	fs << "R2" << R2;
//	fs << "P1" << P1;
//	fs << "P2" << P2;
//	fs << "Q" << Q;
//
//}
//
//
//void fishEyeCircle()
//{
//	double yj[16] = {
//		-4.3, -26.6,  //1#
//		-4.4, -25.1,  //2#
//		-4.8,-46.4, //3#
//		-2.2, -41.9,  //4#
//		-3.6, -47.3	, //5#
//		-7.2,-27.6,  //6#
//		-5.9,-47.6,  //7#
//		-5.3,-27.3 //8#
//	};
//
//	double txd[16] = {
//		-20.1, -9.1, //1#
//		10.6, -28.9, //2#
//		-2.9, 4.1,	//3#
//		1.3, 0.4, //4#
//		-12.6,-34.1, //5#
//		-17,-6.7, //6#
//		8.5, -12.9, //7#
//		12.5,-9.5 //8#
//	};
//
//	string img_path[8]
//	{
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\1.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\2#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\3#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\4#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\5#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\6#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\7#.bmp",
//		"E:\\Users\\Mobius\\Desktop\\0RZ\\8#.bmp",
//	};
//
//
//	for (int i = 0; i < 8; i++)
//	{
//
//		Point2d im_ct = Point2d(800, 600);
//		Mat srcImg = imread(img_path[i]);
//		Point2d yj_oc = Point2d(yj[2 * i], yj[2 * i + 1]);
//		Point2d txd_oc = Point2d(txd[2 * i], txd[2 * i + 1]);
//
//
//
//
//		// 转变换为灰度图
//		Mat grayImg;
//		cvtColor(srcImg, grayImg, CV_BGR2GRAY);
//
//		// 二值化
//		Mat binaryImg;
//		threshold(grayImg, binaryImg, 30, 255, CV_THRESH_BINARY);
//
//		// 找到轮廓
//		vector<vector<Point>> contours;
//		vector<Vec4i> hierarchy;
//		findContours(binaryImg, contours, CV_RETR_LIST, CV_CHAIN_APPROX_SIMPLE);
//
//
//		double max_area = 0.f;
//		int max_area_idx = -1;
//
//		for (size_t i = 0; i < contours.size(); i++)
//		{
//			auto area = contourArea(contours[i]);
//			if (area > max_area)
//			{
//				max_area = area;
//				max_area_idx = i;
//			}
//		}
//
//
//
//		//在原图上画出轮廓
//
//		Mat resultImg = srcImg.clone();
//		drawContours(resultImg, contours, max_area_idx, Scalar(0, 0, 255), 1);
//		//drawContours(resultImg, contours, 1, Scalar(255, 0, 0), 1);
//
//
//		auto rRect = fitEllipse(contours[max_area_idx]);
//		//画出椭圆
//		ellipse(resultImg, rRect, Scalar(0, 255, 0), 2);
//
//		//画出椭圆中心
//		circle(resultImg, rRect.center, 2, Scalar(255, 0, 0), 2);
//
//
//
//		auto sizef = rRect.size;
//		int radius = (sizef.width + sizef.height) / 4;
//
//
//		//circle(srcImg, im_ct, 650, Scalar(255, 255, 255));
//		circle(srcImg, im_ct + yj_oc, 650, Scalar(255, 0, 0));
//		drawMarker(srcImg, im_ct + yj_oc, Scalar(255, 0, 0));
//		//circle(srcImg, im_ct + txd_oc, 650, Scalar(0, 0, 255));
//		circle(srcImg, rRect.center, radius, Scalar(0, 255, 0));
//		drawMarker(srcImg, rRect.center, Scalar(0, 255, 0));
//
//
//
//
//	}
//}
//
////pRGB: RGB Buffer,nWidth*nHeight
////pG : G Channel ，nWidth*nHeight
//
//void extractGChannel(unsigned char* pRGB, int nWidth, int nHeight, unsigned char* pG)
//{
//
//	int nRgbIdx = 0;
//	int nGIdx = 0;
//	for (int y = 0; y < nHeight; y++)
//	{
//		for (int x = 0;x < nWidth; x++)
//		{
//			pG[nGIdx] = pRGB[nRgbIdx + 1];
//			nRgbIdx += 3;
//			nGIdx++;
//		}
//	}
//
//}
//
////传入整张灰度图或者G通道的值
//void calcAveOverThreshod(unsigned char* pImg, int nWidth, int nHeight, int nThreshod, double& ave, int& num)
//{
//	unsigned long long sum = 0;
//	num = 0;
//	for (int y = 0; y < nHeight; y++)
//	{
//		for (int x = nWidth / 2; x < nWidth; x++) //计算右半部分
//		{
//
//			if (pImg[y * nWidth + x] > nThreshod)
//			{
//				sum += pImg[y * nWidth + x];
//				num++;
//			}
//
//		}
//	}
//
//	ave = num == 0 ? 0 :( sum*1.0 / num * 1.0);
//}
//
//
//void flareTest()
//{
//	String imgPaths[] = {
//		"E:\\Users\\Mobius\\Desktop\\73A\\OK_01.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\OK_02.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\NG_01.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\1-5.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\2-5.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\3-5.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\10-5.bmp",
//		"E:\\Users\\Mobius\\Desktop\\73A\\20-5.bmp",
//	};
//
//	int histSize{ 256 };
//	float range[] = { 0, 256 };
//	const float* histRange = { range };
//
//
//
//
//	for (auto imgPath : imgPaths)
//	{
//
//
//		Mat img = imread(imgPath, IMREAD_COLOR);
//		int nWidth = img.cols;
//		int nHeight = img.rows;
//
//		Rect roi(Point(nWidth * 0.7, 0), Point(nWidth, nHeight));
//
//		Mat roiImg = img(roi);
//
//		Mat labImg;
//		cvtColor(roiImg, labImg, COLOR_BGR2Lab);
//
//
//		Mat labImgChannels[3];
//		split(labImg, labImgChannels);
//
//		Mat lCh = labImgChannels[0];
//		Mat aCh = labImgChannels[1];
//		Mat bCh = labImgChannels[2];
//
//		Mat binImg;
//		threshold(lCh, binImg, 60, 255, THRESH_BINARY);
//
//		Mat bBinImg2;
//		threshold(bCh, bBinImg2, 145, 255, THRESH_BINARY);
//
//		double cnt = countNonZero(binImg);
//
//		double cnt2 = countNonZero(bBinImg2);
//
//
//		//unsigned char* pRGBBuffer = img.data;
//		//unsigned char* pGBuffer = new unsigned char[nWidth * nHeight];
//		//extractGChannel(pRGBBuffer, nWidth, nHeight, pGBuffer);
//		//Mat grayImg(Size(nWidth, nHeight), CV_8UC1, pGBuffer);
//		////cvtColor(img, grayImg, CV_BGR2GRAY);
//
//		//Rect roi(Point(nWidth * 0.7, 0), Point(nWidth, nHeight));
//
//		//Mat mask = Mat::zeros(Size(nWidth, nHeight), CV_8UC1);
//		//rectangle(mask, roi, Scalar(255), -1);
//		//
//		////统计直方图
//		//MatND hist;
//		//calcHist(&grayImg, 1, 0, mask, hist, 1, &histSize, &histRange);
//		////显示直方图
//		//int hist_w = 512; int hist_h = 400;
//		//int bin_w = cvRound((double)hist_w / histSize);
//		//Mat histImage(hist_h, hist_w, CV_8UC3, Scalar(0, 0, 0));
//		//normalize(hist, hist, 0, histImage.rows, NORM_MINMAX, -1, Mat());
//		//for (int i = 1; i < histSize; i++)
//		//{
//		//	line(histImage,
//		//		Point(bin_w * (i - 1), hist_h - cvRound(hist.at<float>(i - 1))),
//		//		Point(bin_w * (i), hist_h - cvRound(hist.at<float>(i))),
//		//		Scalar::all(255), 2, 8, 0);
//		//}
//		//imshow("histImage", histImage);
//		//waitKey(0);
//		//destroyAllWindows();
//
//
//		double aveL = mean(lCh, binImg)[0];
//		double aveA = mean(aCh, binImg)[0];
//		double aveB = mean(bCh, binImg)[0];
//
//		double aOverb = aveA / aveB;
//		//double aveL = mean(lCh)[0];
//		//double aveA = mean(aCh)[0];
//		//double aveB = mean(bCh)[0];
//
//		cout << imgPath << ", ave L:" << aveL << ",ave a:" << aveA << ",ave B:" << aveB << ",count1:" << cnt << ",count2:" << cnt2 << ",a/b:" << aOverb << endl;
//
//
//
//
//
//		//double ave{ 0 };
//		//int nCount{ 0 };
//
//		//calcAveOverThreshod(pGBuffer, nWidth, nHeight, 110, ave, nCount);
//		//cout << imgPath << " ,ave:" << ave << " count:" << nCount << endl;
//
//
//
//	}
//}
//
//int makeTemplate(cv::Mat& src, int iRadius)
//{
//
//	auto pData = src.data;
//
//	int nHeight = src.rows;
//	int nWidth = src.cols;
//
//	int cx = nWidth >> 1;
//	int cy = nHeight >> 1;
//	int lmmit = iRadius * iRadius;
//
//	for (int y = 0; y < nHeight; ++y)
//	{
//		for (int x = 0; x < nWidth; ++x)
//		{
//			if (((x - cx) * (x - cx) + (y - cy) * (y - cy)) > lmmit)
//				src.at<uchar>(y, x) = 255;
//		}
//	}
//
//	return 0;
//
//}
//
//int lensDetectTest()
//{
//	String modelPath = "E:\\Users\\Mobius\\Desktop\\S200\\model.bmp";
//	//String testPath = "E:\\Users\\Mobius\\Desktop\\S200\\test.bmp";
//	String testPath = "E:\\Users\\Mobius\\Desktop\\S200\\model.bmp";
//
//
//	Mat modelImg = imread(modelPath, IMREAD_GRAYSCALE);
//	Mat testImg = imread(testPath, IMREAD_GRAYSCALE);
//	double lensIncludeAngle = 45;
//	double thresholdOffset{ 0 };
//	double angleBias{ 3.0 };
//
//
//
//	int iresult{ 0 };
//
//
//	do
//	{
//
//		if (modelImg.empty() || testImg.empty())
//		{
//			cout << "img load failed" << endl;
//			iresult = -1;
//		}
//
//		int modelCx{ 515 }, modelCy{ 647 };
//		int tmpSmallRadius{ 34 };
//		int tmpMidRadius{ 133 };
//		int tmpBigRadius{ 144 };
//
//		Point ct{ modelCx, modelCy };
//		Size szSmall = Size(tmpSmallRadius * 2 + 1, tmpSmallRadius * 2 + 1);
//		Size szMid = Size(tmpMidRadius * 2 + 1, tmpMidRadius * 2 + 1);
//		Size szBig = Size(tmpBigRadius * 2 + 1, tmpBigRadius * 2 + 1);
//
//
//		Rect roiSmall(Point(modelCx - tmpSmallRadius, modelCy - tmpSmallRadius), szSmall);
//		Rect roiMid(Point(modelCx - tmpMidRadius, modelCy - tmpMidRadius), szMid);
//		Rect roiBig(Point(modelCx - tmpBigRadius, modelCy - tmpBigRadius), szBig);
//
//		Mat displayImg;
//		cvtColor(modelImg, displayImg, COLOR_GRAY2BGR);
//
//		RotatedRect rtSmall(ct, szSmall, 0);
//		RotatedRect rtMid(ct, szMid, 0);
//		RotatedRect rtBig(ct, szBig, 0);
//
//		ellipse(displayImg, rtSmall, Scalar(0, 0, 255));
//		ellipse(displayImg, rtMid, Scalar(0, 255, 255));
//		ellipse(displayImg, rtBig, Scalar(255, 0, 0));
//
//
//		Mat tmpSmall = modelImg(roiSmall).clone();
//		Mat tmpMid = modelImg(roiMid).clone();
//		Mat tmpBig = modelImg(roiBig).clone();
//
//
//		char path_small[] = "F:\\Work\\TXD\\AlgorithmDev\\AlgDev2\\x64\\Debug\\small_template.bmp";
//		char path_middle[] = "F:\\Work\\TXD\\AlgorithmDev\\AlgDev2\\x64\\Debug\\middle_template.bmp";
//		char path_big[] = "F:\\Work\\TXD\\AlgorithmDev\\AlgDev2\\x64\\Debug\\big_template.bmp";
//
//
//		iresult = makeTemplate(tmpMid, tmpMidRadius);
//		if (iresult != 0)
//		{
//			break;
//		}
//
//		iresult = makeTemplate(tmpBig, tmpBigRadius);
//		if (iresult != 0)
//		{
//			break;
//		}
//
//		imwrite(path_small, tmpSmall);
//		imwrite(path_middle, tmpMid);
//        imwrite(path_big, tmpBig);
//
//		VisionFrame frame;
//		frame.u8Data = testImg.data;
//		frame.nHeight = testImg.rows;
//		frame.nWidth = testImg.cols;
//
//		VisionFrame smallTemplate;
//		smallTemplate.u8Data = tmpSmall.data;
//		smallTemplate.nHeight = tmpSmall.rows;
//		smallTemplate.nWidth = tmpSmall.cols;
//
//		VisionFrame midTemplate;
//		midTemplate.u8Data = tmpMid.data;
//		midTemplate.nHeight = tmpMid.rows;
//		midTemplate.nWidth = tmpMid.cols;
//
//		VisionFrame bigTemplate;
//		bigTemplate.u8Data = tmpBig.data;
//		bigTemplate.nHeight = tmpBig.rows;
//		bigTemplate.nWidth = tmpBig.cols;
//
//		int centerX{ 0 }, centerY{ 0 };
//		int iradius{ 0 };
//		double dflVisionAngle{ 0 };
//		iresult = HisVisionAFLensB8(frame, smallTemplate, midTemplate, bigTemplate, 4, 80, lensIncludeAngle, 
//			&centerX, &centerY, &dflVisionAngle, &iradius, thresholdOffset, angleBias);
//		
//		testImg = imread(testPath, IMREAD_GRAYSCALE);
//
//		_HisCamera_Frame_Descripte frame2;
//		frame2.iheightpixel = testImg.rows;
//        frame2.iiwidthpixel = testImg.cols;
//        frame2.iwidthbyte = testImg.cols;
//        frame2.pucbuffer = testImg.data;
//        frame2.ucformat = _HisVision_Frame_Format_Mono8;
//		int centerX2{ 0 }, centerY2{ 0 };
//		int iradius2{ 0 };
//		double dflVisionAngle2{ 0 };
//        iresult = HisVisionAFLensB8(frame2, path_small, path_middle, path_big, 4, 80, 
//			lensIncludeAngle, &centerX2, &centerY2, &dflVisionAngle2, &iradius2, thresholdOffset, angleBias);
//
//		
//
//
//	} while (false);
//
//
//
//
//
//	return iresult;
//}
//
//
//int loadRawData(unsigned short* pDst,int nWidth, int nHeight, const string& imgPath)
//{
//    std::ifstream ifs(imgPath, ios::binary);
//    if (!ifs.is_open())
//    {
//        cout << "open file failed" << endl;
//        return -1;
//    }
//
//	
//
//	int fileSize = nWidth * nHeight * 2;
//
//    ifs.read((char*)pDst, fileSize);
//
//	if (ifs.gcount() != fileSize)
//	{
//		return -2;
//	}
//
//	return 0;
//}
//
//void hanldeBoundary(unsigned short* pSrcRaw, int nWidth, int nHeight)
//{
//	pSrcRaw[nWidth - 1] = (pSrcRaw[nWidth - 2] + pSrcRaw[2 * nWidth - 1] + pSrcRaw[2 * nWidth - 2]) / 3;
//
//	for (int y = 0; y < nHeight;  y++ )
//	{	
//		int nIdx = y * nWidth;
//		pSrcRaw[nIdx] = pSrcRaw[nIdx + 1];
//	}
//
//}
//
//void defectPixelDetect(unsigned short* pSrcRaw, int nWidth, int nHeight, int filterSize, int nTresh)
//{	
//
//	Mat src(nHeight, nWidth, CV_16UC1, pSrcRaw, nWidth * 2);
//	Mat fltImg;
//	src.convertTo(fltImg, CV_64FC1);
//	Mat sumImg;
//
//	boxFilter(fltImg, sumImg, -1, Size(filterSize, filterSize), Point(-1, -1), false, BORDER_REFLECT_101);
//	sumImg = sumImg - fltImg;
//	sumImg = sumImg / (filterSize * filterSize - 1);
//	sumImg = abs(sumImg - fltImg);
//
//	Mat binImg;
//	threshold(sumImg, binImg, nTresh, 255, THRESH_BINARY);
//
//	
//
//}
//
//
//int blemishForToF()
//{
//	string imgPath1 = "E:\\Users\\Mobius\\Desktop\\ADS6401\\blemish\\20.raw";
//	string imgPath2 = "E:\\Users\\Mobius\\Desktop\\ADS6401\\blemish\\21.raw";
//
//	int nWidth = 210;
//	int nHeight = 160;
//    unsigned short* pSrc1 = new unsigned short[nWidth * nHeight];
//    unsigned short* pSrc2 = new unsigned short[nWidth * nHeight];
//
//	int iret = 0;
//
//	do 
//	{
//		iret = loadRawData(pSrc1, nWidth, nHeight, imgPath1);
//        if (iret != 0)
//		{
//			break;
//		}
//        iret = loadRawData(pSrc2, nWidth, nHeight, imgPath2);
//
//		Mat src1(nHeight, nWidth, CV_16UC1, pSrc1, nWidth*2);
//		Mat src2(nHeight, nWidth, CV_16UC1, pSrc2, nWidth*2);
//
//
//
//		hanldeBoundary((unsigned short*)src1.data, nWidth, nHeight);
//		hanldeBoundary((unsigned short*)src2.data, nWidth, nHeight);
//
//		defectPixelDetect(pSrc1, nWidth, nHeight, 11, 10);
//
//
//
//		Mat bin1, bin2;
//		Mat srcF1, srcF2;
//		//src1.convertTo(srcF1, CV_32FC1);
//		//src2.convertTo(srcF2, CV_32FC1);
//		//threshold(srcF1, bin1, 30, 255, THRESH_BINARY );
//		//threshold(srcF2, bin2, 30, 255, THRESH_BINARY );
//
//		threshold(src1, bin1, 30, 255, THRESH_BINARY);
//		threshold(src2, bin2, 30, 255, THRESH_BINARY);
//
//
//		//calcH
//		// 计算图像中心和最大半径
//		int nWidth = src2.cols;
//		int nHeight = src2.rows;
//		auto imgSize = src2.size();
//		int cx = nWidth / 2;
//		int cy = nHeight / 2;
//		double r_max = sqrt(pow(cx, 2) + pow(cy, 2));
//
//		// 创建四个区域的掩膜
//		vector<Mat> masks(4);
//		for (int i = 0; i < 4; ++i)
//			masks[i] = Mat::zeros(imgSize, CV_8UC1);
//
//		// 遍历每个像素计算所属区域
//		for (int y = 0; y < nHeight; ++y) {
//			for (int x = 0; x < nWidth; ++x) {
//				double dx = x - cx;
//				double dy = y - cy;
//				double r = sqrt(dx * dx + dy * dy) / r_max;
//			
//				if (x == 0 || (y==0 && x == nWidth - 1))
//				{
//					continue;
//				}
//				if (r < 0.3)        masks[0].at<uchar>(y, x) = 255;
//				else if (r < 0.5)   masks[1].at<uchar>(y, x) = 255;
//				else if (r < 0.7)   masks[2].at<uchar>(y, x) = 255;
//				else                masks[3].at<uchar>(y, x) = 255;
//			}
//		}
//
//		// 设置直方图参数
//		int histSize = 65536;
//		float range[] = { 0, 65535 };
//		const float* histRange = { range };
//		vector<Mat> hists(4);
//
//		// 计算各区域直方图
//		for (int i = 0; i < 4; ++i) {
//			calcHist(&src2, 1, 0, masks[i], hists[i], 1, &histSize, &histRange);
//			//normalize(hists[i], hists[i], 0, 1, NORM_MINMAX); // 归一化
//		}
//
//
//		for (int i = 0; i < 4; ++i)
//		{
//			double minVal, maxVal;
//			minMaxLoc(src2, & minVal, & maxVal, nullptr, nullptr, masks[i]);
//			double meanVal = mean(src2, masks[i])[0];
//			cout << "max:" << maxVal << ",min:" << minVal << ",mean:" << meanVal << endl;
//
//		}
//
//
//
//
//
//
//
//	} while (false);
//
//
//	return iret;
//
//}


using cv::Point;
using std::vector;


float getAngle(const cv::Point2f& a, const cv::Point2f& b, const cv::Point2f& c, cv::Point2f& d)
{
	auto angle1 = atan2(b.y - a.y, b.x - a.x);
	auto angle2 = atan2(d.y - c.y, d.x - c.x);
	auto dstAngle = static_cast<float>(abs((angle1 - angle2) * 180.f / CV_PI));
	return dstAngle;
}

void testTilt()
{

	Point ptTL, ptTR, ptBL, ptBR;
	cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\T1.bmp";
	Mat grayImage = imread(imgPath, IMREAD_GRAYSCALE);
	Mat binaryImg;
    threshold(grayImage, binaryImg, 35, 255, THRESH_BINARY);

	int	iWidth = grayImage.cols;
    int iHeight = grayImage.rows;

	vector<vector<Point>> contours;
	findContours(binaryImg, contours, RETR_TREE, CHAIN_APPROX_TC89_KCOS, Point());



	Point2f pointTL{ iWidth * 1.f, iHeight * 1.f }, pointTR{ 0.f, iHeight * 1.f }, pointBL{ iWidth * 1.f,0.f }, pointBR{ 0.f,0.f };
	bool isFindTL = false, isFindTR = false, isFindBL = false, isFindBR = false;


	float left = iWidth * 3 / 10;
	float right = iWidth * 7 / 10;
	float top = iHeight * 3 / 10;
	float bottom = iHeight * 7 / 10;

	float flMaxArea = iWidth * iHeight / 20;

	vector<vector<Point>> candidate;
	for (auto& contour : contours)
	{
		auto rRect = minAreaRect(contour);
		auto flArea = contourArea(contour);

		cv::Moments moment = cv::moments(contour);
		cv::Point2f ct = cv::Point2f(float(moment.m10 / moment.m00), float(moment.m01 / moment.m00));

		float flRatio = rRect.size.height / rRect.size.width;


		if (flArea > 2000 && flArea < flMaxArea && flRatio > 0.8 && flRatio < 1.2)
		{
#ifdef _DEBUG
			candidate.push_back(contour);
			drawContours(grayImage, candidate, candidate.size() - 1, Scalar::all(255));
#endif

			//top left
			if (ct.x < left && ct.y < top && ct.x < pointTL.x)
			{
				pointTL = ct;
				ptTL.x = pointTL.x;
				ptTL.y = pointTL.y;
				isFindTL = true;
			}

			if (ct.x > right && ct.y < top && ct.x > pointTR.x)
			{
				pointTR = ct;
				ptTR.x = pointTR.x;
				ptTR.y = pointTR.y;
				isFindTR = true;
			}

			if (ct.x < left && ct.y > bottom && ct.x < pointBL.x)
			{
				pointBL = ct;
				ptBL.x = pointBL.x;
				ptBL.y = pointBL.y;
				isFindBL = true;
			}

			if (ct.x > right && ct.y > bottom && ct.x > pointBR.x)
			{
				pointBR = ct;
				ptBR.x = pointBR.x;
				ptBR.y = pointBR.y;
				isFindBR = true;
			}
		}
	}


	bool isPass = isFindTL && isFindTR && isFindBL && isFindBR;
	if (!isPass)
	{

		return;
		//nRet = GEN_ALG_ERROR_CODE(EC_ALG_IQ_UNSATISFY);
	}
#ifdef _DEBUG
	drawMarker(grayImage, pointTL, Scalar::all(255), MARKER_CROSS);
	drawMarker(grayImage, pointTR, Scalar::all(255), MARKER_CROSS);
	drawMarker(grayImage, pointBL, Scalar::all(255), MARKER_CROSS);
	drawMarker(grayImage, pointBR, Scalar::all(255), MARKER_CROSS);

#endif

	float fConvergenceAngleH = getAngle(pointTL, pointTR, pointBL, pointBR);
	float fConvergenceAngleV = getAngle(pointTL, pointBL, pointTR, pointBR);



	return;




}

void testCodCalc()
{


	cv::String imgPath = "E:\\Users\\Mobius\\Desktop\\图片\\2AV_ChessBoard\\4E4C4E322D173E0CT\\_8_gray.bmp";

	Mat imgSrc = imread(imgPath, IMREAD_GRAYSCALE);

	imgSrc.convertTo(imgSrc, CV_64FC1);

	CodCalc codCalc;
	auto imNormalized =	codCalc.normalization_fft(imgSrc, 10.0);


}



int main(int argc, char const* argv[])
{
	//testTilt();

	testCodCalc();

	//return blemishForToF();

	//flareTest();

	//return	lensDetectTest();

	return 0;
}





