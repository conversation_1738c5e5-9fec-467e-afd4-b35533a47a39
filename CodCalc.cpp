﻿#include "CodCalc.h"

#include <Eigen/Dense>
#include <ceres/ceres.h>

//#define  OPTIMIZED_GAUSSIAN_WINDOW


Mat CodCalc::makeWindow(int height, int width, double sigma /*= 10.0*/)
{

#ifndef OPTIMIZED_GAUSSIAN_WINDOW
	cv::Mat window(height, width, CV_64F); // 使用 double 类型存储

	double xcenter = (width - 1.0) / 2.0;
	double ycenter = (height - 1.0) / 2.0;
	double num = 2.0 * sigma * sigma;

	for (int y = 0; y < height; ++y)
	{
		for (int x = 0; x < width; ++x)
		{
			double dx = x - xcenter;
			double dy = y - ycenter;
			double value = std::exp(-(dx * dx + dy * dy) / num);
			window.at<double>(y, x) = value;
		}
	}

	return window;
#else
	// Create 1D Gaussian kernels for rows and columns
	cv::Mat gaussianX = cv::getGaussianKernel(width, sigma, CV_64F);
	cv::Mat gaussianY = cv::getGaussianKernel(height, sigma, CV_64F);

	// Create 2D Gaussian window by multiplying the 1D kernels
	cv::Mat window = gaussianY * gaussianX.t();

	// 去归一化：把中心调到 1（消掉 getGaussianKernel 的归一化常数）
	const int cy = height / 2; // 对偶数尺寸，这里等价于 floor((h-1)/2+0.5)，与公式中心一致
	const int cx = width / 2;
	double peak = window.at<double>(cy, cx);
	if (peak != 0.0) window /= peak;

	return window;

#endif
}

Mat CodCalc::applyFFTFilter(const cv::Mat& mat, double sigma, int pad, int borderType /*= cv::BORDER_REFLECT*/)
{
	CV_Assert(mat.type() == CV_64F); // 输入必须是 double
	int h = mat.rows, w = mat.cols;

	// --- 1. padding ---
	cv::Mat padded;
	cv::copyMakeBorder(mat, padded, pad, pad, pad, pad, borderType);

	int H = padded.rows, W = padded.cols;

	// --- 2. 高斯窗 ---
	cv::Mat window = makeWindow(H, W, sigma);

	// --- 3. matsign = (-1)^(x+y) ---
	cv::Mat matsign(H, W, CV_64F);
	for (int y = 0; y < H; ++y)
		for (int x = 0; x < W; ++x)
			matsign.at<double>(y, x) = ((x + y) % 2 == 0) ? 1.0 : -1.0;

	cv::Mat shifted = padded.mul(matsign);

	// --- 4. FFT ---
	cv::Mat planes[] = { shifted, cv::Mat::zeros(H, W, CV_64F) };
	cv::Mat complexMat;
	cv::merge(planes, 2, complexMat);
	cv::dft(complexMat, complexMat, cv::DFT_COMPLEX_OUTPUT);

	// --- 5. 乘以高斯窗 ---
	cv::split(complexMat, planes);
	cv::Mat realPart = planes[0].mul(window);
	cv::Mat imagPart = planes[1].mul(window);
	cv::merge(std::vector<cv::Mat>{realPart, imagPart}, complexMat);

	// --- 6. 逆 FFT ---
	cv::dft(complexMat, complexMat, cv::DFT_INVERSE | cv::DFT_SCALE | cv::DFT_COMPLEX_OUTPUT);
	cv::split(complexMat, planes);

	cv::Mat filtered = planes[0].mul(matsign); // 取实部并乘回 matsign

	// --- 7. 去掉 padding ---
	cv::Rect roi(pad, pad, w, h);
	return filtered(roi).clone();
}

cv::Mat CodCalc::normalization_fft(const cv::Mat& mat, double sigma /*= 10.0*/, int pad /*= 100*/, int borderType /*= cv::BORDER_REFLECT_101*/)
{
	// Step 1: 估计背景


	Mat mat_bck = applyFFTFilter(mat, sigma, pad, borderType);

	// Step 2: 计算背景均值
	cv::Scalar mean_val_scalar = cv::mean(mat_bck);
	double mean_val = mean_val_scalar[0];


	Mat mask = mat_bck == 0.0;

	auto mat_bck_no0 = mat_bck.clone();
    mat_bck_no0.setTo(mean_val, mask);

	Mat mat_cor;
	cv::divide(mat, mat_bck_no0, mat_cor);
	mat_cor *= mean_val;
	return mat_cor;
}

std::vector<cv::Mat> gradient(const cv::Mat& f, int edge_order) {
	// 检查edge_order参数
	if (edge_order > 2) {
		return {};
	}

	// 检查输入数组的大小
	if (f.rows < edge_order + 1 || f.cols < edge_order + 1) {
		return {};
	}

	// 转换输入为double类型
	cv::Mat f_double;
	f.convertTo(f_double, CV_64F);

	// 创建结果数组
	cv::Mat grad_y = cv::Mat::zeros(f.size(), CV_64F);
	cv::Mat grad_x = cv::Mat::zeros(f.size(), CV_64F);

	// 使用统一间距为1.0
	double dx = 1.0;

	// 使用Sobel算子计算内部区域的梯度（更高效）
	cv::Mat interior_y, interior_x;

	// 计算内部区域的梯度
	if (f.rows > 2 && f.cols > 2) {
		// 提取内部区域（去除边界）
		cv::Rect interior_rect(1, 1, f.cols - 2, f.rows - 2);
		cv::Mat interior = f_double(interior_rect);

		// 使用Sobel算子计算内部梯度
		cv::Mat kernel_y = (cv::Mat_<double>(3, 1) << -1, 0, 1) / (2.0 * dx);
		cv::Mat kernel_x = (cv::Mat_<double>(1, 3) << -1, 0, 1) / (2.0 * dx);

		cv::filter2D(interior, interior_y, CV_64F, kernel_y);
		cv::filter2D(interior, interior_x, CV_64F, kernel_x);

		// 将计算结果复制到结果矩阵的内部区域
		interior_y.copyTo(grad_y(interior_rect));
		interior_x.copyTo(grad_x(interior_rect));
	}
	else {
		// 如果矩阵太小，使用原始方法计算内部点
		for (int i = 1; i < f.rows - 1; i++) {
			for (int j = 0; j < f.cols; j++) {
				grad_y.at<double>(i, j) = (f_double.at<double>(i + 1, j) - f_double.at<double>(i - 1, j)) / (2.0 * dx);
			}
		}

		for (int i = 0; i < f.rows; i++) {
			for (int j = 1; j < f.cols - 1; j++) {
				grad_x.at<double>(i, j) = (f_double.at<double>(i, j + 1) - f_double.at<double>(i, j - 1)) / (2.0 * dx);
			}
		}
	}

	// 处理边界
	if (edge_order == 1) {
		// 一阶边缘差分 - 使用向量化操作
		cv::Mat first_row = f_double.row(1) - f_double.row(0);
		cv::Mat last_row = f_double.row(f.rows - 1) - f_double.row(f.rows - 2);
		first_row.copyTo(grad_y.row(0));
		last_row.copyTo(grad_y.row(f.rows - 1));
		grad_y.row(0) /= dx;
		grad_y.row(f.rows - 1) /= dx;

		cv::Mat first_col = f_double.col(1) - f_double.col(0);
		cv::Mat last_col = f_double.col(f.cols - 1) - f_double.col(f.cols - 2);
		first_col.copyTo(grad_x.col(0));
		last_col.copyTo(grad_x.col(f.cols - 1));
		grad_x.col(0) /= dx;
		grad_x.col(f.cols - 1) /= dx;
	}
	else {
		// 二阶边缘差分 - 使用矩阵操作
		double a_first = -1.5 / dx;
		double b_first = 2.0 / dx;
		double c_first = -0.5 / dx;

		double a_last = 0.5 / dx;
		double b_last = -2.0 / dx;
		double c_last = 1.5 / dx;

		// 垂直方向（y方向）边界
		if (f.rows >= 3) {
			// 第一行
			cv::Mat row0 = a_first * f_double.row(0) + b_first * f_double.row(1) + c_first * f_double.row(2);
			row0.copyTo(grad_y.row(0));

			// 最后一行
			cv::Mat rowLast = a_last * f_double.row(f.rows - 3) + b_last * f_double.row(f.rows - 2) + c_last * f_double.row(f.rows - 1);
			rowLast.copyTo(grad_y.row(f.rows - 1));
		}
		else {
			// 矩阵太小，使用原始方法
			for (int j = 0; j < f.cols; j++) {
				grad_y.at<double>(0, j) = a_first * f_double.at<double>(0, j) + b_first * f_double.at<double>(1, j);
				if (f.rows > 2) grad_y.at<double>(0, j) += c_first * f_double.at<double>(2, j);

				grad_y.at<double>(f.rows - 1, j) = c_last * f_double.at<double>(f.rows - 1, j) + b_last * f_double.at<double>(f.rows - 2, j);
				if (f.rows > 2) grad_y.at<double>(f.rows - 1, j) += a_last * f_double.at<double>(f.rows - 3, j);
			}
		}

		// 水平方向（x方向）边界
		if (f.cols >= 3) {
			// 第一列
			cv::Mat col0 = a_first * f_double.col(0) + b_first * f_double.col(1) + c_first * f_double.col(2);
			col0.copyTo(grad_x.col(0));

			// 最后一列
			cv::Mat colLast = a_last * f_double.col(f.cols - 3) + b_last * f_double.col(f.cols - 2) + c_last * f_double.col(f.cols - 1);
			colLast.copyTo(grad_x.col(f.cols - 1));
		}
		else {
			// 矩阵太小，使用原始方法
			for (int i = 0; i < f.rows; i++) {
				grad_x.at<double>(i, 0) = a_first * f_double.at<double>(i, 0) + b_first * f_double.at<double>(i, 1);
				if (f.cols > 2) grad_x.at<double>(i, 0) += c_first * f_double.at<double>(i, 2);

				grad_x.at<double>(i, f.cols - 1) = c_last * f_double.at<double>(i, f.cols - 1) + b_last * f_double.at<double>(i, f.cols - 2);
				if (f.cols > 2) grad_x.at<double>(i, f.cols - 1) += a_last * f_double.at<double>(i, f.cols - 3);
			}
		}
	}

	return { grad_y, grad_x };
}



cv::Mat CodCalc::convert_chessboard_to_linepattern(const cv::Mat& mat, bool smooth /*= true*/, bool bright /*= true*/, int sigma /*= 3*/)
{
	CV_Assert(mat.channels() == 1); // 输入应为单通道灰度图
	cv::Mat img = mat.clone();

	// 1. Gaussian smoothing
	if (smooth) {
		cv::GaussianBlur(img, img, cv::Size(0, 0), sigma, sigma, cv::BORDER_REPLICATE);
	}

	// 2. Gradient
	//cv::Mat grad_x, grad_y;
	//cv::Sobel(img, grad_x, CV_64F, 1, 0, 3, 1, 0, cv::BORDER_DEFAULT);
	//cv::Sobel(img, grad_y, CV_64F, 0, 1, 3, 1, 0, cv::BORDER_DEFAULT);

	//cv::Mat abs_grad_x = cv::abs(grad_x);
	//cv::Mat abs_grad_y = cv::abs(grad_y);

	auto grads = gradient(img, 1);
	cv::Mat abs_grad_x = cv::abs(grads[0]);
	cv::Mat abs_grad_y = cv::abs(grads[1]);

	cv::Mat mat_line = 0.5 * (abs_grad_x + abs_grad_y);

	// 3. crop + pad
	int crop = smooth ? 4 : 2;
	cv::Rect roi(crop, crop, mat_line.cols - 2 * crop, mat_line.rows - 2 * crop);
	cv::Mat cropped = mat_line(roi);

	cv::Mat padded;
	cv::copyMakeBorder(cropped, padded, crop, crop, crop, crop, cv::BORDER_REPLICATE);

	mat_line = padded;

	// 4. background inversion if bgr == "bright"
	if (bright) {
		double minVal, maxVal;
		cv::minMaxLoc(mat_line, &minVal, &maxVal);
		mat_line = maxVal - mat_line;
	}

	// 5. normalization
	cv::Scalar meanAbs = cv::mean(cv::abs(mat_line));
	if (meanAbs[0] > 1e-9) {
		mat_line /= meanAbs[0];
	}

	return mat_line;
}

cv::Mat CodCalc::select_roi(const cv::Mat& mat, float ratio, bool square /*= false*/)
{
	CV_Assert(mat.channels() == 1); // 假设输入是单通道图像

	int height = mat.rows;
	int width = mat.cols;

	// clip ratio to [0.05, 1.0]
	ratio = std::max(0.05f, std::min(1.0f, ratio));

	cv::Rect roi;
	if (square) {
		int c_hei = height / 2;
		int c_wid = width / 2;
		int radi = static_cast<int>(ratio * std::min(height, width)) / 2;

		int x = std::max(0, c_wid - radi);
		int y = std::max(0, c_hei - radi);
		int w = std::min(width - x, 2 * radi);
		int h = std::min(height - y, 2 * radi);

		roi = cv::Rect(x, y, w, h);
	}
	else {
		int depad_hei = static_cast<int>((height - ratio * height) / 2.0f);
		int depad_wid = static_cast<int>((width - ratio * width) / 2.0f);

		int x = depad_wid;
		int y = depad_hei;
		int w = width - 2 * depad_wid;
		int h = height - 2 * depad_hei;

		roi = cv::Rect(x, y, w, h);
	}

	return mat(roi).clone(); // 返回 ROI 副本，避免引用原图
}

cv::Mat CodCalc::make_circle_mask(int width, float ratio)
{
	cv::Mat mask = cv::Mat::zeros(width, width, CV_64F);
	int center = width / 2;
	int radius = static_cast<int>(ratio * center);
	cv::circle(mask, cv::Point(center, center), radius, cv::Scalar(1.0f), -1);
	return mask;
}

struct GaussFunctor {
	GaussFunctor(const std::vector<double>& x, const std::vector<double>& y)
		: x_(x), y_(y) {
	}

	template <typename T>
	bool operator()(const T* const params, T* residuals) const {
		T a = params[0];
		T b = params[1];
		T c = params[2];
		T d = params[3];
		for (size_t i = 0; i < x_.size(); ++i) {
			T yi = T(y_[i]);
			T diff = T(x_[i]) - c;
			T fi = a * exp(- diff * diff / (2.0 * b * b)) + d;
			residuals[i] = yi - fi;
		}
		return true;
	}

	const std::vector<double>& x_;
	const std::vector<double>& y_;
};

void CodCalc::get_gauss_peak_fit(const std::vector<double>& list_data, std::vector<double>& fit_data, 
	double& peak_c, double& offset, bool& check)
{
	int npoint = (int)list_data.size();
	std::vector<double> list_x(npoint);
	for (int i = 0; i < npoint; ++i) {
		list_x[i] = i - npoint / 2;
	}

	check = false;
	fit_data.resize(npoint);

	// 初始猜测
	double params[4] = { 1.0, 1.0, 0.0, 0.0 };

	ceres::Problem problem;
	ceres::CostFunction* cost_function =
		new ceres::AutoDiffCostFunction<GaussFunctor, ceres::DYNAMIC, 4>(
			new GaussFunctor(list_x, list_data), npoint);
	problem.AddResidualBlock(cost_function, nullptr, params);

	ceres::Solver::Options options;
	options.max_num_iterations = 50;
	options.linear_solver_type = ceres::DENSE_QR;
	options.minimizer_progress_to_stdout = false;

	ceres::Solver::Summary summary;
	ceres::Solve(options, &problem, &summary);

	if (summary.termination_type == ceres::CONVERGENCE ||
		summary.final_cost < 1e-6) {
		check = true;
	}

	double a = params[0];
	double b = params[1];
	double c = params[2];
	double d = params[3];
	for (int i = 0; i < npoint; ++i) {
		fit_data[i] = gauss_function(list_x[i], a, b, c, d);
	}
	peak_c = c;
	offset = d;
}

double CodCalc::locate_subpixel_point(const std::vector<double>& list_point, bool bMin /*= true*/)
{
	size_t n = list_point.size();
	Eigen::VectorXd x(n), y(n);
	for (size_t i = 0; i < n; i++)
	{
		x(i) = static_cast<double>(i);
		y(i) = list_point[i];
	}

	Eigen::MatrixXd A(n, 3);
	for (size_t i = 0; i < n; i++) {
		A(i, 0) = x(i) * x(i);
		A(i, 1) = x(i);
		A(i, 2) = 1.0;
	}
	Eigen::VectorXd coeff = A.colPivHouseholderQr().solve(y);
	double a = coeff(0), b = coeff(1);
	double pos = bMin ?
		(double)(std::min_element(list_point.begin(), list_point.end()) - list_point.begin()) :
		(double)(std::max_element(list_point.begin(), list_point.end()) - list_point.begin());
	if (abs(a) > 1e-9)
	{
		double num = -b / (2 * a);

		if (num >= 0 && num < n)
		{
			if ((bMin && a > 0) || (!bMin && a < 0))
			{
				return num;
			}
		}
	}
	return pos;
}

double CodCalc::calcStd(const std::vector<double>& list_data)
{
	if (list_data.empty()) {
		return std::numeric_limits<double>::quiet_NaN();
	}
	// 计算均值
	double sum = std::accumulate(list_data.begin(), list_data.end(), 0.0);
	double mean = sum / list_data.size();

	// 计算方差（分母 n-1）
	double variance = 0.0;
	for (double x : list_data) {
		variance += std::pow(x - mean, 2);
	}
	variance /= (list_data.size() - 1);  // 样本方差分母为 n-1

	return std::sqrt(variance);
}

std::vector<int> CodCalc::select_good_peaks(std::vector<double> list_data, const std::vector<int>& peaks, 
	double tol /*= 0.2*/, int radius /*= 11*/, double sigma /*= 0*/, bool use_offset /*= true*/)
{
	// 高斯平滑
	if (sigma > 0) {
		cv::Mat src(1, (int)list_data.size(), CV_64F, list_data.data());
		cv::Mat dst;
		cv::GaussianBlur(src, dst, cv::Size(0, 0), sigma);
		list_data.assign((double*)dst.datastart, (double*)dst.dataend);
	}

	std::vector<int> good_peaks;
	int npoint = (int)list_data.size();
	int half_radius = radius / 2;

	for (int p : peaks) {
		int start = std::max(0, p - radius);
		int stop = std::min(npoint, p + radius + 1);
		if ((stop - start) > 3)
		{
			std::vector<double> list_sub(list_data.begin() + start, list_data.begin() + stop);
			double stdv = calcStd(list_sub);

			if (stdv != 0.0)
			{
				auto minmax = std::minmax_element(list_sub.begin(), list_sub.end());
				double minv = *minmax.first;
				std::vector<double> list_norm(list_sub.size());
				for (size_t i = 0; i < list_sub.size(); i++) {
					list_norm[i] = (list_sub[i] - minv) / stdv;
				}

				std::vector<double> fit_data;
				double del_x = 0.0, offset = 0.0;
				bool check = false;
				get_gauss_peak_fit(list_norm, fit_data, del_x, offset, check);

				// 计算 80% 分位数
				std::vector<double> diff(list_norm.size());
				for (size_t i = 0; i < list_norm.size(); i++) {
					diff[i] = std::abs(fit_data[i] - list_norm[i]);
				}

				auto [minVal, maxVal] = std::minmax_element(diff.begin(), diff.end());
				double num = (*maxVal - *minVal) * 0.8 + *minVal;


				if (check && std::abs(del_x) < half_radius && num < tol) {
					if (use_offset) {
						if (std::abs(offset) < tol) {
							good_peaks.push_back(p);
						}
					}
					else {
						good_peaks.push_back(p);
					}
				}
			}
		}
	}
	return good_peaks;
}

std::vector<double> CodCalc::get_local_extrema_points(const std::vector<double>& list_data_in,
	bool bMin /*= true*/, int radius /*= 7*/, double sensitive /*= 0.1*/, bool denoise /*= true*/,
	bool norm /*= true*/, bool subpixel /*= true*/, bool select_peaks_flag /*= false*/)
{
	// copy input
	std::vector<double> list_data = list_data_in;
	const int num_point = static_cast<int>(list_data.size());
	if (num_point < 3)
		return {};
	// denoise using OpenCV Gaussian filter (sigma = 3, similar to scipy.ndimage.gaussian_filter(..., 3))
	if (denoise)
	{
		// Create a 1xN cv::Mat of type CV_64F
		cv::Mat src(1, num_point, CV_64F);
		for (int i = 0; i < num_point; ++i)
			src.at<double>(0, i) = list_data[i];
		cv::Mat dst;
		// ksize = 0 -> auto, sigmaX = 3
		cv::GaussianBlur(src, dst, cv::Size(0, 0), 3.0, 3.0, cv::BORDER_REPLICATE);
		for (int i = 0; i < num_point; ++i)
			list_data[i] = dst.at<double>(0, i);
	}

	// If option == "max", invert data: new = max - old
	if (!bMin) {
		double mx = *std::max_element(list_data.begin(), list_data.end());
		for (int i = 0; i < num_point; ++i)
			list_data[i] = mx - list_data[i];
	}

	// clip radius to [1, num_point//4]
	int max_radius = std::max(1, num_point / 4);
	radius = std::min(std::max(radius, 1), max_radius);

	// normalization: fit a linear background from central quarter of sorted-by-value samples
	if (norm) {
		// Build (x,y) pairs and sort by y (value)
		std::vector<std::pair<double, double>> combined;
		std::vector<double> xlist;
		combined.reserve(num_point);
		for (int i = 0; i < num_point; ++i)
		{
			xlist.push_back(static_cast<double>(i));
			combined.emplace_back(static_cast<double>(i), list_data[i]);
		}
		std::sort(combined.begin(), combined.end(),
			[](const auto& a, const auto& b) { return a.second < b.second; });

		int ndrop = static_cast<int>(0.25 * num_point);
		// Ensure at least one sample remains for fitting
		int start = ndrop;
		int end = num_point - ndrop - 1;

		// Prepare vectors for linear fit using Eigen (y = a1 * x + a0)
		int fit_n = end - start;
		Eigen::MatrixXd A(fit_n, 2);
		Eigen::VectorXd Y(fit_n);
		for (int i = 0; i < fit_n; ++i) {
			A(i, 0) = i;
			A(i, 1) = 1.0;
			Y(i) = combined[start + i].second;
		}
		// Solve least squares for [a1, a0]
		Eigen::Vector2d alpha = A.colPivHouseholderQr().solve(Y);
		double a1 = alpha(0);
		double a0 = alpha(1);
		// compute list_fit = a1 * xlist + a0
		std::vector<double> list_fit(num_point);
		for (int i = 0; i < num_point; ++i)
			list_fit[i] = a1 * static_cast<double>(i) + a0;

		double l_thres = a0;
		double u_thres = a1 * static_cast<double>(num_point - 1) + a0;

		// list_sort is combined second (y) values sorted ascending
		std::vector<double> list_sort(num_point);
		for (int i = 0; i < num_point; ++i) {
			auto& val = list_fit[i];
			if (val >= l_thres && val <= u_thres)
			{
				combined[i].second = val;
			}
		}


		std::sort(combined.begin(), combined.end(),
			[](const auto& a, const auto& b) { return a.first < b.first; });
		// nmean = mean(abs(list_fit))
		double sumAbs = 0.0;
		for (double v : list_fit)
			sumAbs += std::abs(v);
		double nmean = sumAbs / num_point;

		// Divide list_data by backgr where backgr != 0; else set to nmean
		for (int i = 0; i < num_point; ++i) {
			auto& backgr = combined[i].second;
			if (backgr != 0.0)
				list_data[i] = list_data[i] / backgr;
			else
				list_data[i] = nmean;
		}
	}

	// Find local extrema (minima in this implementation, but if option=="max" we inverted earlier)
	std::vector<int> points;
	for (int i = radius; i < num_point - radius - 1; ++i) {
		double val = list_data[i];
		// get window [i-radius, i+radius]
		std::vector<double> window;
		window.reserve(2 * radius + 1);
		for (int k = i - radius; k <= i + radius; ++k)
			window.push_back(list_data[k]);
		std::vector<double> window_sorted = window;
		std::sort(window_sorted.begin(), window_sorted.end());

		double num1 = window_sorted[0] - val;
		// compute mean of top 'radius' largest values in window (window_sorted[-radius:])
		double sumTop = 0.0;
		for (int t = 0; t < radius; ++t) {
			sumTop += window_sorted[window_sorted.size() - 1 - t];
		}
		double nmean_top = sumTop / radius;
		double num2 = (std::abs(nmean_top) > 0.0) ? std::abs((val - nmean_top) / nmean_top) : 0.0;

		if (std::abs(num1) < 1e-12 && num2 > sensitive) {
			points.push_back(i);
		}
	}

	// Optionally select good peaks using your select_good_peaks implementation.
	if (select_peaks_flag) {
		// prepare inverted data for select_good_peaks: np.max(list_data)-list_data
		double mx = *std::max_element(list_data.begin(), list_data.end());
		std::vector<double> inverted(list_data.size());
		for (size_t i = 0; i < list_data.size(); ++i)
			inverted[i] = mx - list_data[i];

		// call your select_good_peaks (uses its default tol/radius/etc.)
		std::vector<int> filtered = select_good_peaks(inverted, points /* peaks */);
		points.swap(filtered);
	}

	// If subpixel true, refine using locate_subpixel_point over 3-sample window
	if (subpixel) {
		std::vector<double> points_sub;
		points_sub.reserve(points.size());
		if (!points.empty()) {
			for (int idx : points) {
				// make sure i-1..i+1 are within bounds (should be, given loop bounds earlier)
				if (idx - 1 < 0 || idx + 1 >= num_point)
					continue;
				std::vector<double> small_win = { list_data[idx - 1], list_data[idx], list_data[idx + 1] };
				// locate_subpixel_point expects vector<double> and returns sub-sample offset (likely in [0,2])
				double offset = locate_subpixel_point(small_win, true); // true -> looking for minima
				double pos_sub = static_cast<double>(idx - 1) + offset;
				points_sub.push_back(pos_sub);
			}
		}
		return points_sub;
	}

	// otherwise return integer positions as doubles
	std::vector<double> res;
	res.reserve(points.size());
	for (int p : points)
		res.push_back(static_cast<double>(p));
	return res;
}

std::vector<double> CodCalc::genAngle(double start, double stop_inclusive, double step)
{
	std::vector<double> v;
	if (step <= 0) return v;
	for (double a = start; a <= stop_inclusive + 1e-12; a += step)
		v.push_back(a);
	return v;
}

Mat scipyAffineToOpenCV(const Mat& M_scipy) {
	CV_Assert(M_scipy.rows == 3 && M_scipy.cols == 3);

	// SciPy matrix is output->input, so invert it
	Mat M_inv = M_scipy.inv();

	// OpenCV warpAffine wants 2x3
	Mat warpMat = M_inv(cv::Rect(0, 0, 3, 2)).clone();
	return warpMat;
}

cv::Mat CodCalc::radon(const cv::Mat& image, const std::vector<double>& theta /*= {}*/, /* 投影角度（单位：度） */
	bool circle /*= true*/, /* 是否使用内接圆裁剪 */ bool preserve_range /*= false*/) // 是否保留原始范围（这里等效）
{
	if (image.channels() != 1) {
		return cv::Mat();
	}
	if (image.empty()) {
		return cv::Mat();
	}

	// 转换到 float
	cv::Mat img;
	if (!preserve_range)
		image.convertTo(img, CV_64F);
	else
		image.convertTo(img, CV_64F, 1.0 / 255.0);  // 类似 skimage.img_as_float

	// 默认角度 0~179
	std::vector<double> angles = theta;
	if (angles.empty()) {
		angles.resize(180);
		for (int i = 0; i < 180; i++) 
			angles[i] = static_cast<double>(i);
	}

	cv::Mat padded;
	if (circle) {
		int shape_min = std::min(img.rows, img.cols);
		int radius = shape_min / 2;
		int cy = img.rows / 2;
		int cx = img.cols / 2;

		// mask 裁剪圆外区域
		cv::Mat mask = cv::Mat::zeros(img.size(), CV_8U);
		cv::circle(mask, cv::Point(cx, cy), radius, cv::Scalar(255), -1);
		cv::Mat cropped;
		img.copyTo(cropped, mask);

		// 裁成正方形
		int start_y = (img.rows - shape_min) / 2;
		int start_x = (img.cols - shape_min) / 2;
		cv::Rect roi(start_x, start_y, shape_min, shape_min);
		padded = cropped(roi).clone();
	}
	else {
		// pad 成对角线大小的正方形
		double diagonal = std::sqrt(2.0) * std::max(img.rows, img.cols);
		int new_size = static_cast<int>(std::ceil(diagonal));
		int top = (new_size - img.rows) / 2;
		int bottom = new_size - img.rows - top;
		int left = (new_size - img.cols) / 2;
		int right = new_size - img.cols - left;
		cv::copyMakeBorder(img, padded, top, bottom, left, right, cv::BORDER_CONSTANT, cv::Scalar(0));
	}

	if (padded.rows != padded.cols) {
		throw std::runtime_error("padded_image must be square.");
	}

	int N = padded.rows;
	int center = N / 2;

	// 输出 sinogram: 大小 N x num_angles
	cv::Mat radon_image = cv::Mat::zeros(N, (int)angles.size(), CV_64F);

	for (size_t i = 0; i < angles.size(); i++) {
		double angle = angles[i] * CV_PI / 180.0;
		double cos_a = std::cos(angle);
		double sin_a = std::sin(angle);

		// 仿射矩阵
		cv::Mat R = (cv::Mat_<double>(3, 3) <<
			cos_a, sin_a, -center * (cos_a + sin_a - 1),
			-sin_a, cos_a, -center * (cos_a - sin_a - 1),
			0, 0, 1);

		cv::Mat R_inv = scipyAffineToOpenCV(R);
		cv::Mat rotated;
		cv::warpAffine(padded, rotated, R_inv, padded.size(),
			cv::INTER_LINEAR, cv::BORDER_CONSTANT, cv::Scalar(0));

		// 沿列方向求和 (对应 Python 的 sum(0))
		cv::Mat projection;
		cv::reduce(rotated, projection, 0, cv::REDUCE_SUM, CV_64F);

		// 存入结果 (projection 是 1xN，需要写入 Nx1 列)
		for (int r = 0; r < N; r++) {
			radon_image.at<double>(r, (int)i) = projection.at<double>(0, r);
		}
	}

	return radon_image;
}

cv::Mat CodCalc::gradient_along_axis(const cv::Mat& src, int axis)
{
	CV_Assert(src.type() == CV_64F);
	cv::Mat grad = cv::Mat::zeros(src.size(), src.type());

	int rows = src.rows;
	int cols = src.cols;

	if (axis == 0) // y方向 (行)
	{
		for (int r = 0; r < rows; ++r)
		{
			for (int c = 0; c < cols; ++c)
			{
				if (r == 0) // forward difference
					grad.at<double>(r, c) = src.at<double>(r + 1, c) - src.at<double>(r, c);
				else if (r == rows - 1) // backward difference
					grad.at<double>(r, c) = src.at<double>(r, c) - src.at<double>(r - 1, c);
				else // central difference
					grad.at<double>(r, c) = (src.at<double>(r + 1, c) - src.at<double>(r - 1, c)) * 0.5f;
			}
		}
	}
	else if (axis == 1) // x方向 (列)
	{
		for (int r = 0; r < rows; ++r)
		{
			for (int c = 0; c < cols; ++c)
			{
				if (c == 0) // forward difference
					grad.at<double>(r, c) = src.at<double>(r, c + 1) - src.at<double>(r, c);
				else if (c == cols - 1) // backward difference
					grad.at<double>(r, c) = src.at<double>(r, c) - src.at<double>(r, c - 1);
				else // central difference
					grad.at<double>(r, c) = (src.at<double>(r, c + 1) - src.at<double>(r, c - 1)) * 0.5f;
			}
		}
	}
	return grad;
}

std::pair<double, double> CodCalc::calc_slope_distance_hor_lines(const cv::Mat& mat_in, bool chessboard/* = false*/,
	double ratio /*= 0.3*/,double search_range /*= 30.0*/, int radius /*= 9*/, double sensitive /*= 0.1*/, bool bright /*= true*/,
	bool denoise /*= true*/, bool norm /*= true*/, bool subpixel /*= true*/, bool select_peaks /*= false*/)
{
	cv::Mat mat = mat_in.clone();

	// 1. 若是棋盘格图像，先转换为线条图案
	if (chessboard) {
		mat = convert_chessboard_to_linepattern(mat);
	}

	// 2. 可选高斯去噪
	if (denoise) {
		cv::GaussianBlur(mat, mat, cv::Size(0, 0), 3);
	}

	// 3. ROI（中心区域，正方形）
	cv::Mat mat_roi = select_roi(mat, ratio, true);

	// 4. 背景亮度处理
	if (bright) {
		double minVal, maxVal;
		cv::minMaxLoc(mat_roi, &minVal, &maxVal);
		mat_roi = maxVal - mat_roi;
	}

	// 5. 初步搜索角度
	std::vector<double> angle_coarse;
	for (int i = -static_cast<int>(search_range); i <= static_cast<int>(search_range); i++) {
		angle_coarse.push_back(90.0 + i);
	}

	// 6. 圆形掩膜
	cv::Mat mask = make_circle_mask(mat_roi.rows, 0.92);
	cv::Mat mat_masked;
	cv::multiply(mat_roi, mask, mat_masked);

	// 7. 粗搜索 Radon 变换
	cv::Mat sinogram1 = radon(mat_masked, angle_coarse, true);
	cv::Mat list_max1;
	cv::reduce(sinogram1, list_max1, 0, cv::REDUCE_MAX);

	cv::Point pos_max1;
	cv::minMaxLoc(list_max1, nullptr, nullptr, nullptr, &pos_max1);
	double best_angle1 = angle_coarse[pos_max1.x];

	// 8. 精细搜索角度
	std::vector<double> angle_fine;
	for (double a = best_angle1 - 1.0; a <= best_angle1 + 1.0001; a += 0.05) {
		angle_fine.push_back(a);
	}

	cv::Mat sinogram2 = radon(mat_masked, angle_fine, true);
	cv::Mat list_max2;
	cv::reduce(sinogram2, list_max2, 0, cv::REDUCE_MAX);

	cv::Point pos_max2;
	cv::minMaxLoc(list_max2, nullptr, nullptr, nullptr, &pos_max2);

	double best_angle2 = -(angle_fine[pos_max2.x] - 90.0);

	// 9. 斜率（弧度）
	double slope = std::tan(best_angle2 * CV_PI / 180.0);

	// 10. 提取 Radon 投影（最佳角度对应的列）
	std::vector<double> sinogram_col(sinogram2.rows);
	for (int i = 0; i < sinogram2.rows; i++) {
		sinogram_col[i] = sinogram2.at<double>(i, pos_max2.x);
	}

	// 极值点检测（这里 max 对应 Python 的 option="max"）
	std::vector<double> list_ext_point = get_local_extrema_points(
		sinogram_col,
		false, // bMin=false -> 找最大值
		radius,
		sensitive,
		denoise,
		norm,
		subpixel,
		select_peaks
	);

	// 11. 计算行间距
	double distance = 0.0;
	if (list_ext_point.size() > 3) {
		std::vector<double> diffs;
		for (size_t i = 1; i < list_ext_point.size(); i++) {
			diffs.push_back(std::abs(list_ext_point[i] - list_ext_point[i - 1]));
		}
		std::nth_element(diffs.begin(), diffs.begin() + std::ceil(diffs.size() / 2.0), diffs.end());

		if (diffs.size() % 2 ==1)
		{
			distance = diffs[diffs.size() / 2]; // 中位数
		}
		else
		{
			distance = (diffs[diffs.size() / 2] + diffs[diffs.size() / 2 - 1]) / 2;
		}

	}
	else if (list_ext_point.size() > 1) {
		std::vector<double> diffs;
		for (size_t i = 1; i < list_ext_point.size(); i++) {
			diffs.push_back(std::abs(list_ext_point[i] - list_ext_point[i - 1]));
		}
		distance = std::accumulate(diffs.begin(), diffs.end(), 0.0) / diffs.size(); // 平均值
	}

	return { slope, distance };
}

std::pair<double, double> CodCalc::calc_slope_distance_ver_lines(const cv::Mat& mat_in, bool chessboard /*= false*/, 
	double ratio /*= 0.3*/, double search_range /*= 30.0*/, int radius /*= 9*/, double sensitive /*= 0.1*/,
	bool bright /*= true*/, bool denoise /*= true*/, bool norm /*= true*/, bool subpixel /*= true*/, bool select_peaks /*= false*/)
{
	cv::Mat mat = mat_in.clone();

	// 1. 如果是棋盘格 -> 转换为线条图案
	if (chessboard) {
		mat = convert_chessboard_to_linepattern(mat);
	}

	// 2. 去噪：高斯滤波
	if (denoise) {
		cv::GaussianBlur(mat, mat, cv::Size(0, 0), 3);
	}

	// 3. ROI 取中心区域
	cv::Mat mat_roi = select_roi(mat, static_cast<float>(ratio), true);

	// 4. 背景亮度处理（Python: bright=反色）
	if (bright) {
		double minVal, maxVal;
		cv::minMaxLoc(mat_roi, &minVal, &maxVal);
		mat_roi = maxVal - mat_roi;
	}

	// 5. 粗略角度搜索
	std::vector<double> angle_coarse;
	for (double a = -search_range; a <= search_range + 1.0; a += 1.0) {
		angle_coarse.push_back(a);
	}

	cv::Mat mask = make_circle_mask(mat_roi.rows, 0.92);
	cv::Mat mat_masked;
	cv::multiply(mat_roi, mask, mat_masked);

	cv::Mat sinogram1 = radon(mat_masked, angle_coarse, true);

	std::vector<double> list_max1(sinogram1.cols, 0.0);
	for (int c = 0; c < sinogram1.cols; c++) {
		double maxVal;
		cv::minMaxLoc(sinogram1.col(c), nullptr, &maxVal);
		list_max1[c] = maxVal;
	}

	int pos_max1 = static_cast<int>(std::distance(list_max1.begin(),
		std::max_element(list_max1.begin(), list_max1.end())));
	double best_angle1 = angle_coarse[pos_max1];

	// 6. 精细角度搜索
	std::vector<double> angle_fine;
	for (double a = best_angle1 - 1.0; a <= best_angle1 + 1.0001; a += 0.05) {
		angle_fine.push_back(a);
	}

	cv::Mat sinogram2 = radon(mat_masked, angle_fine, true);

	std::vector<double> list_max2(sinogram2.cols, 0.0);
	for (int c = 0; c < sinogram2.cols; c++) {
		double maxVal;
		cv::minMaxLoc(sinogram2.col(c), nullptr, &maxVal);
		list_max2[c] = maxVal;
	}

	int pos_max2 = static_cast<int>(std::distance(list_max2.begin(),
		std::max_element(list_max2.begin(), list_max2.end())));
	double best_angle2 = angle_fine[pos_max2];

	// 7. 计算斜率（弧度）
	double slope = std::tan(best_angle2 * CV_PI / 180.0);

	// 8. 提取局部极值点
	std::vector<double> sinogram_col(sinogram2.rows);
	for (int r = 0; r < sinogram2.rows; r++) {
		sinogram_col[r] = sinogram2.at<double>(r, pos_max2);
	}

	std::vector<double> list_ext_point =
		get_local_extrema_points(sinogram_col, false, radius, sensitive, denoise, norm, subpixel, select_peaks);

	// 9. 计算线间距
	double distance = 0.0;
	if (list_ext_point.size() > 1) {
		std::vector<double> diffs(list_ext_point.size() - 1);
		for (size_t i = 1; i < list_ext_point.size(); i++) {
			diffs[i - 1] = std::abs(list_ext_point[i] - list_ext_point[i - 1]);
		}

		if (list_ext_point.size() > 3) {
			std::nth_element(diffs.begin(), diffs.begin() + std::ceil(diffs.size() / 2.0), diffs.end());

			if (diffs.size() % 2 == 1)
			{
				distance = diffs[diffs.size() / 2]; // 中位数
			}
			else
			{
				distance = (diffs[diffs.size() / 2] + diffs[diffs.size() / 2 - 1]) / 2;
			}
		}
		else {
			double sum = std::accumulate(diffs.begin(), diffs.end(), 0.0);
			distance = sum / diffs.size();      // 均值
		}
	}

	return { slope, distance };
}

std::pair<int, int> CodCalc::calc_index_range(int height, int width, double angle_deg, bool isHorizontal)
{
	double angle = angle_deg * CV_PI / 180.0;
	int min_idx = 0;
	int max_idx = 0;

	if (isHorizontal) {
		if (std::abs(angle_deg - 90.0) <= 1e-6) {
			//throw std::invalid_argument(
			//	"If the input angle is around 90-degree, use the 'vertical' option "
			//	"and update the angle to around 0-degree instead!!!");
			return { -1, -1 };
		}
		else {
			if (angle_deg > 0) {
				min_idx = static_cast<int>(std::ceil(width * std::tan(angle)));
				max_idx = height - 1;
			}
			else {
				min_idx = 0;
				max_idx = height - 1 - static_cast<int>(std::floor(width * std::tan(std::abs(angle))));
			}
			if (min_idx < 0 || min_idx >= height || max_idx < 0 || max_idx >= height) {
				//throw std::invalid_argument(
				//	"Row index is out of range, please select the direction correctly !!!");
				return { -1, -1 };
			}
		}
	}
	else {
		if (std::abs(angle_deg - 90.0) == 1e-6) {
			//throw std::invalid_argument(
			//	"If the input angle is around 90-degree, use the 'horizontal' option "
			//	"and update the angle to around 0-degree instead!!!");
			return { -1, -1 };
		}
		else {
			if (angle_deg > 0) {
				min_idx = 0;
				max_idx = width - 1 - static_cast<int>(std::ceil(height * std::tan(angle)));
			}
			else {
				min_idx = static_cast<int>(std::floor(height * std::tan(std::abs(angle))));
				max_idx = width - 1;
			}
			if (min_idx < 0 || min_idx >= width || max_idx < 0 || max_idx >= width) {
				//throw std::invalid_argument(
				//	"Column index is out of range, please select the direction correctly !!!");
				return { -1, -1 };
			}
		}
	}

	return { min_idx, max_idx };
}

std::vector<double> CodCalc::sliding_window_slope(const std::vector<double>& list_data, int size /*= 3*/, bool norm /*= true*/)
{
	int npoint = static_cast<int>(list_data.size());
	if (npoint < 3) {
		return {};
	}

	// 限制窗口大小在 [3, npoint]
	size = std::max(3, std::min(size, npoint));
	if (size % 2 == 0) {
		size += 1; // 保证奇数
	}
	int radius = size / 2;

	// 边界 padding（edge 模式，等于 Python np.pad(..., mode="edge")）
	std::vector<double> padded_data;
	padded_data.reserve(npoint + 2 * radius);
	// 前端 pad
	for (int i = 0; i < radius; i++) {
		padded_data.push_back(list_data.front());
	}
	// 原始数据
	padded_data.insert(padded_data.end(), list_data.begin(), list_data.end());
	// 后端 pad
	for (int i = 0; i < radius; i++) {
		padded_data.push_back(list_data.back());
	}

	// x_list = 0,1,...,size-1
	Eigen::VectorXd x_list(size);
	for (int i = 0; i < size; i++) {
		x_list(i) = static_cast<double>(i);
	}

	// 输出 slopes
	std::vector<double> slopes(npoint, 0.0);

	for (int i = 0; i < npoint; i++) {
		// 局部数据
		Eigen::VectorXd y(size);
		for (int j = 0; j < size; j++) {
			y(j) = padded_data[i + j];
		}

		// 构造设计矩阵 A = [x, 1]
		Eigen::MatrixXd A(size, 2);
		A.col(0) = x_list;
		A.col(1) = Eigen::VectorXd::Ones(size);

		// 最小二乘解: coeff = (A^T A)^(-1) A^T y
		Eigen::Vector2d coeff = A.colPivHouseholderQr().solve(y);

		double slope = coeff(0);  // 取斜率
		slopes[i] = std::abs(slope);
	}

	// 归一化
	if (norm) {
		double mean_val = std::accumulate(slopes.begin(), slopes.end(), 0.0) / slopes.size();
		if (std::abs(mean_val) > 1e-12) {
			for (auto& v : slopes) {
				v /= mean_val;
			}
		}
	}

	return slopes;
}


double CodCalc::cubicInterpolate(const cv::Mat& img, double y, double x)
{
	int h = img.rows, w = img.cols;
	int ix = static_cast<int>(std::floor(x));
	int iy = static_cast<int>(std::floor(y));
	double dx = x - ix;
	double dy = y - iy;

	auto getVal = [&](int yy, int xx) {
		yy = std::clamp(yy, 0, h - 1);
		xx = std::clamp(xx, 0, w - 1);
		return img.at<double>(yy, xx);
		};

	Eigen::Matrix<double, 4, 4> patch;
	for (int m = -1; m <= 2; ++m) {
		for (int n = -1; n <= 2; ++n) {
			patch(m + 1, n + 1) = getVal(iy + m, ix + n);
		}
	}

	auto cubic = [](double p0, double p1, double p2, double p3, double t) {
		return p1 + 0.5 * t * (p2 - p0 +
			t * (2.0 * p0 - 5.0 * p1 + 4.0 * p2 - p3 +
				t * (3.0 * (p1 - p2) + p3 - p0)));
		};

	// 先对每一行做 cubic，再对结果做 cubic
	Eigen::Vector4d col;
	for (int m = 0; m < 4; ++m) {
		col(m) = cubic(patch(m, 0), patch(m, 1), patch(m, 2), patch(m, 3), dx);
	}
	return cubic(col(0), col(1), col(2), col(3), dy);
}

std::tuple<std::vector<double>, std::vector<double>, std::vector<double>> CodCalc::get_tilted_profile(
	const cv::Mat& mat, int index, double angle_deg, bool isHorizontal)
{
	if (mat.channels() != 1)
		throw std::runtime_error("Input must be a 2D array !!!");

	int height = mat.rows, width = mat.cols;

	// 你已有的函数：calc_index_range(height, width, angle_deg, isHorizontal)
	auto range = calc_index_range(height, width, angle_deg, isHorizontal);
	int min_idx = range.first;
	int max_idx = range.second;

	double angle = angle_deg * CV_PI / 180.0;
	if (index < min_idx || index > max_idx)
		return {};

	std::vector<double> xlist, ylist, profile;

	if (isHorizontal) {
		int N = width;
		double max_r = std::floor(width / std::cos(angle));
		for (int i = 0; i < N; i++) {
			double r = max_r * i / (N - 1);
			double x = r * std::cos(angle);
			double y = index + r * std::sin(-angle);
			x = std::clamp(x, 0.0, (double)width - 1);
			y = std::clamp(y, 0.0, (double)height - 1);
			xlist.push_back(x);
			ylist.push_back(y);
			profile.push_back(cubicInterpolate(mat, y, x));
		}
	}
	else { // vertical
		int N = height;
		double max_r = std::floor(height / std::cos(angle));
		for (int i = 0; i < N; i++) {
			double r = max_r * i / (N - 1);
			double y = r * std::cos(angle);
			double x = index + r * std::sin(angle);
			x = std::clamp(x, 0.0, (double)width - 1);
			y = std::clamp(y, 0.0, (double)height - 1);
			xlist.push_back(x);
			ylist.push_back(y);
			profile.push_back(cubicInterpolate(mat, y, x));
		}
	}

	return { xlist, ylist, profile };
}

std::vector<cv::Point2d> CodCalc::get_cross_points_hor_lines(const cv::Mat& mat_in, double slope_ver, double dist_ver, bool chessboard /*= false*/, int radius /*= 11*/, double sensitive /*= 0.1*/, double ratio /*= 0.3*/, bool norm /*= true*/, int offset /*= 0*/, bool bright /*= true*/, bool denoise /*= true*/, bool subpixel /*= true*/, bool select_peaks /*= false*/)
{
	if (mat_in.channels() != 1)
		return {};

	int height = mat_in.rows;
	int width = mat_in.cols;

	cv::Mat mat = mat_in.clone();

	// ---- 背景反转 (bright → 线条黑) ----
	if (bright) {
		double maxVal;
		cv::minMaxLoc(mat, nullptr, &maxVal);
		mat = maxVal - mat;
	}

	// ---- 归一化 (FFT 方法在这里简化为均值归一化) ----
	if (norm) {
		mat = normalization_fft(mat, 5);
	}

	// ---- 去噪 (Gaussian blur, sigma=3) ----
	if (denoise) {
		cv::GaussianBlur(mat, mat, cv::Size(0, 0), 3);
	}

	// ---- 计算角度 (rad) ----
	double angle = std::atan(slope_ver);
	double angle_deg = angle * 180.0 / CV_PI;

	// ---- 行范围 ----
	auto range = calc_index_range(height, width, angle_deg, false);
	int min_row = range.first;
	int max_row = range.second;

	offset = std::clamp(offset, 0, std::min(height, width) / 3);

	std::vector<cv::Point2d> list_points;

	// ---- 扫描生成直线 ----
	for (double i = min_row + offset; i < max_row - offset; i += ratio * dist_ver) {
		// 获取倾斜 profile
		auto [xlist, ylist, profile] = get_tilted_profile(mat, (int)i, angle_deg, false);

		if (xlist.empty() || ylist.empty() || profile.empty())
			continue;

		// 计算 scale
		double scale = std::sqrt(std::pow(xlist.back() - xlist.front(), 2.0) +
			std::pow(ylist.back() - ylist.front(), 2.0)) / (height - 1);

		// 如果 chessboard = true → profile 替换成 slope
		if (chessboard) {
			profile = sliding_window_slope(profile, 3, true);
		}

		// 极值点
		std::vector<double> rlist = get_local_extrema_points(
			profile, false, radius, sensitive, !denoise, !norm, subpixel, select_peaks);

		// 缩放
		for (auto& r : rlist)
			r *= scale;

		// 转换到 (x, y)
		for (auto r : rlist) {
			double x1 = r * std::sin(angle) + xlist.front();
			double y1 = r * std::cos(angle) + ylist.front();
			list_points.emplace_back(x1, y1);
		}
	}

	return list_points;
}

std::vector<cv::Point2d> CodCalc::get_cross_points_ver_lines(const cv::Mat& mat_in, double slope_hor, double dist_hor, bool chessboard /*= false*/, int radius /*= 11*/, double sensitive /*= 0.1*/, double ratio /*= 0.3*/, bool norm /*= true*/, int offset /*= 0*/, bool bright /*= true*/, bool denoise /*= true*/, bool subpixel /*= true*/, bool select_peaks /*= false*/)
{
	if (mat_in.channels() != 1)
		return {};
	cv::Mat mat = mat_in.clone();
	int height = mat.rows;
	int width = mat.cols;

	// 1. 背景反转 (bright -> 线暗)
	if (bright) {
		double maxVal;
		cv::minMaxLoc(mat, nullptr, &maxVal);
		mat = maxVal - mat;
	}

	// 2. 归一化 (FFT归一化这里假设已有函数，你提供的 prep.normalization_fft)
	if (norm) {
		mat = normalization_fft(mat, 5);
	}

	// 3. 高斯平滑
	if (denoise) {
		cv::GaussianBlur(mat, mat, cv::Size(0, 0), 3);
	}

	// 4. 角度
	double angle = std::atan(slope_hor);
	auto [min_col, max_col] = calc_index_range(height, width, -angle * 180.0 / CV_PI, true);

	// 5. 偏移裁剪
	offset = std::clamp(offset, 0, std::min(height, width) / 8);

	std::vector<cv::Point2d> list_points;

	for (double i = min_col + offset; i < max_col - offset; i += ratio * dist_hor) {
		// 获取倾斜剖面
		auto [xlist, ylist, profile] = get_tilted_profile(mat, static_cast<int>(i), -angle * 180.0 / CV_PI, true);

		if (xlist.empty() || ylist.empty() || profile.empty()) continue;

		// 计算缩放因子
		double scale = std::sqrt(std::pow(xlist.back() - xlist.front(), 2) +
			std::pow(ylist.back() - ylist.front(), 2)) / (width - 1);

		std::vector<double> profile_proc = profile;

		// 棋盘格处理
		if (chessboard) {
			profile_proc = sliding_window_slope(profile_proc, 3, true);
		}

		// 极值点检测
		std::vector<double> rlist = get_local_extrema_points(
			profile_proc,
			false,   // Python 里 option="max" -> bMin = false
			radius,
			sensitive,
			!denoise,
			!norm,
			subpixel,
			select_peaks
		);

		// 转换到 (x,y) 坐标
		for (double r : rlist) {
			double rr = r * scale;
			double x1 = rr * std::cos(angle) + xlist.front();
			double y1 = rr * std::sin(angle) + ylist.front();
			list_points.emplace_back(x1, y1);
		}
	}

	return list_points;
}

cv::Mat CodCalc::make_parabola_mask(int height, int width, double hor_curviness /*= 0.3*/, 
	double ver_curviness /*= 0.3*/, std::pair<int, int> hor_margin /*= { 100,100 }*/,
	std::pair<int, int> ver_margin /*= { 100,100 }*/, double rotate /*= 0.0*/)
{
	int top_margin = ver_margin.first;
	int bot_margin = ver_margin.second;
	int left_margin = hor_margin.first;
	int right_margin = hor_margin.second;

	if ((left_margin + right_margin) > width) {
		return {};
	}
	if ((top_margin + bot_margin) > height) {
		return {};
	}

	cv::Mat mask(height, width, CV_64F, cv::Scalar(1.0));

	// 遍历像素生成抛物线边界
	for (int y = 0; y < height; y++) {
		double* row_ptr = mask.ptr<double>(y);
		for (int x = 0; x < width; x++) {
			// 顶部 parabola_y
			double parabola_top = (ver_curviness / width) * std::pow(x - width / 2.0, 2) + top_margin;
			double parabola_bot = -(ver_curviness / width) * std::pow(x - width / 2.0, 2) + height - bot_margin;

			// 左右 parabola_x
			double parabola_left = (hor_curviness / height) * std::pow(y - height / 2.0, 2) + left_margin;
			double parabola_right = -(hor_curviness / height) * std::pow(y - height / 2.0, 2) + width - right_margin;

			// 在 mask 内部的条件
			if (!((y > parabola_top) && (y < parabola_bot) &&
				(x > parabola_left) && (x < parabola_right)))
			{
				row_ptr[x] = 0.0;
			}
		}
	}

	// 旋转 mask
	if (std::abs(rotate) > 1e-6) {
		cv::Point2f center(width / 2.0f, height / 2.0f);
		cv::Mat rot_mat = cv::getRotationMatrix2D(center, rotate, 1.0);
		cv::warpAffine(mask, mask, rot_mat, mask.size(), cv::INTER_NEAREST, cv::BORDER_CONSTANT, cv::Scalar(0));
		cv::threshold(mask, mask, 0.5, 1.0, cv::THRESH_BINARY); // np.round 效果
	}

	return mask;
}

std::vector<cv::Point2d> CodCalc::remove_points_using_parabola_mask(const std::vector<cv::Point2d>& points,
	int height, int width, double hor_curviness /*= 0.3*/, double ver_curviness /*= 0.3*/, 
	std::pair<int, int> hor_margin /*= { 100, 100 }*/, std::pair<int, int> ver_margin /*= { 100, 100 }*/,
	double rotate /*= 0.0*/)
{
	// 生成抛物线 mask
	cv::Mat mask = make_parabola_mask(height, width,
		hor_curviness, ver_curviness,
		hor_margin, ver_margin, rotate);

	std::vector<cv::Point2d> filtered_points;
	filtered_points.reserve(points.size());

	for (const auto& pt : points) {
		int y = static_cast<int>(std::round(pt.y));
		int x = static_cast<int>(std::round(pt.x));

		if (y >= 0 && y < height && x >= 0 && x < width) {
			if (mask.at<double>(y, x) == 1.0f) {
				filtered_points.push_back(pt);
			}
		}
	}

	return filtered_points;
}

std::vector<cv::Point2d> CodCalc::rotate_points(const std::vector<cv::Point2d>& points, double angle, bool degree_unit /*= true*/)
{
	if (degree_unit) {
		angle = angle * CV_PI / 180.0;  // 角度转弧度
	}

	double cosA = std::cos(angle);
	double sinA = std::sin(angle);

	std::vector<cv::Point2d> rotated;
	rotated.reserve(points.size());

	for (const auto& pt : points) {
		double y = pt.y;
		double x = pt.x;
		double xr = x * cosA - y * sinA;
		double yr = x * sinA + y * cosA;
		rotated.emplace_back(xr, yr);  // 保持 (y, x) 顺序
	}

	return rotated;
}

bool CodCalc::check_dot_on_line_hor(const cv::Point2d& dot1, const cv::Point2d& dot2,
	double slope, double dot_dist, double ratio, int num_dot_miss)
{
	double dist_error = ratio * dot_dist;
	double search_dist = num_dot_miss * dot_dist;

	double xmin = dot1.x - search_dist;
	double xmax = dot1.x + search_dist;
	if (!(dot2.x > xmin && dot2.x < xmax)) {
		return false;
	}

	// 计算 dot2 到直线 (y = slope*x + b, b = y1 - slope*x1) 的垂直距离
	double ntemp1 = std::sqrt(slope * slope + 1.0);
	double ntemp2 = dot1.y - slope * dot1.x;
	double dist_d12 = std::abs(slope * dot2.x - dot2.y + ntemp2) / ntemp1;

	return (dist_d12 < dist_error);
}

bool CodCalc::check_dot_on_line_ver(const cv::Point2d& dot1, const cv::Point2d& dot2, 
	double slope, double dot_dist, double ratio, int num_dot_miss)
{
	double dist_error = ratio * dot_dist;
	double search_dist = num_dot_miss * dot_dist;

	double ymin = dot1.y - search_dist;
	double ymax = dot1.y + search_dist;
	if (!(dot2.y > ymin && dot2.y < ymax)) {
		return false;
	}

	// 计算 dot2 到直线 (y = slope*x + b, b = y1 - slope*x1) 的垂直距离
	double ntemp1 = std::sqrt(slope * slope + 1.0);
	double ntemp2 = dot1.x - slope * dot1.y;
	double dist_d12 = std::abs(slope * dot2.y - dot2.x + ntemp2) / ntemp1;

	return (dist_d12 < dist_error);

}

std::vector<std::vector<cv::Point2d>> CodCalc::group_dots_hor_lines(const std::vector<cv::Point2d>& list_dots, 
	double slope, double dot_dist, double ratio /*= 0.3*/, int num_dot_miss /*= 6*/, double accepted_ratio /*= 0.65*/)
{
	int num_dots = static_cast<int>(list_dots.size());
	if (num_dots == 0) {
		return {};
	}
	std::vector<cv::Point2d> list_dots_left = list_dots;
	std::sort(list_dots_left.begin(), list_dots_left.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) { return a.x < b.x; });

	std::vector<std::vector<cv::Point2d>> list_lines;

	while (list_dots_left.size() > 1) {
		cv::Point2d dot1 = list_dots_left.front();
		std::vector<cv::Point2d> dots_selected{ dot1 };
		std::vector<int> pos_get{ 0 };

		for (int i = 1; i < static_cast<int>(list_dots_left.size()); i++) {
			cv::Point2f dot2 = list_dots_left[i];
			if (check_dot_on_line_hor(dot1, dot2, slope, dot_dist, ratio, num_dot_miss)) {
				dot1 = dot2;
				dots_selected.push_back(dot2);
				pos_get.push_back(i);
			}
		}

		// 剔除已分配的点
		std::vector<cv::Point2d> new_left;
		for (int i = 0; i < static_cast<int>(list_dots_left.size()); i++) {
			if (std::find(pos_get.begin(), pos_get.end(), i) == pos_get.end()) {
				new_left.push_back(list_dots_left[i]);
			}
		}
		list_dots_left.swap(new_left);

		if (dots_selected.size() > 1) {
			list_lines.push_back(dots_selected);
		}
	}

	// 过滤短的线
	std::vector<int> list_len;
	for (auto& line : list_lines)
		list_len.push_back(static_cast<int>(line.size()));

	int len_max = *std::max_element(list_len.begin(), list_len.end());
	int len_accepted = static_cast<int>(accepted_ratio * len_max);

	std::vector<std::vector<cv::Point2d>> lines_selected;
	for (auto& line : list_lines) {
		if (static_cast<int>(line.size()) > len_accepted) {
			lines_selected.push_back(line);
		}
	}

	// 按平均 y 排序
	std::sort(lines_selected.begin(), lines_selected.end(),
		[](const std::vector<cv::Point2d>& a, const std::vector<cv::Point2d>& b) {
			double mean_a = 0.0, mean_b = 0.0;
			for (auto& p : a) mean_a += p.y;
			for (auto& p : b) mean_b += p.y;
			mean_a /= a.size();
			mean_b /= b.size();
			return mean_a < mean_b;
		});

	return lines_selected;
}

Eigen::VectorXd CodCalc::polyfit(const std::vector<double>& x, const std::vector<double>& y, int order)
{
	int N = static_cast<int>(x.size());
	Eigen::MatrixXd A(N, order + 1);
	Eigen::VectorXd Y(N);

	for (int i = 0; i < N; ++i) {
		double val = 1.0;
		for (int j = 0; j <= order; ++j) {
			A(i, j) = val;
			val *= x[i];
		}
		Y(i) = y[i];
	}

	// 最小二乘解
	Eigen::VectorXd coeff = A.colPivHouseholderQr().solve(Y);
	return coeff;
}

double CodCalc::polyval(const Eigen::VectorXd& coeff, double x)
{
	double result = 0.0;
	double xn = 1.0;
	for (int i = 0; i < coeff.size(); ++i) {
		result += coeff[i] * xn;
		xn *= x;
	}
	return result;
}

std::vector<cv::Point2d> CodCalc::get_nearby_hor_points(const std::vector<cv::Point2d>& current_points,
	const std::vector<cv::Point2d>& points, double residual, int order /*= 2*/)
{
	std::vector<double> cx, cy;
	for (const auto& pt : current_points) {
		cx.push_back(pt.x);  // 列 (x)
		cy.push_back(pt.y);  // 行 (y)
	}

	Eigen::VectorXd coeff = polyfit(cx, cy, order);

	std::vector<cv::Point2d> nearby_points;
	for (const auto& pt : points) {
		double y_pred = polyval(coeff, pt.x);
		double dist = std::abs(pt.y - y_pred);
		if (dist <= residual) {
			nearby_points.push_back(pt);
		}
	}

	return nearby_points;
}

void CodCalc::unique_points(std::vector<cv::Point2d>& points)
{
	std::sort(points.begin(), points.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			if (a.x == b.x) return a.y < b.y;
			return a.x < b.x;
		});
	points.erase(std::unique(points.begin(), points.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return (abs(a.x - b.x) < 1e-6 && abs(a.y - b.y) < 1e-6);
		}),
		points.end());
}

std::vector<cv::Point2d> CodCalc::get_nearby_hor_points_iter(const std::vector<cv::Point2d>& initial_points,
	const std::vector<cv::Point2d>& points, double x_left, double x_right, double search_dist, double residual,
	double overlap_ratio /*= 0.5*/, int order /*= 2*/)
{
	overlap_ratio = std::clamp(overlap_ratio, 0.0, 1.0);
	double overlap = search_dist * overlap_ratio;

	double xr_curr = x_right;
	double xr_next = xr_curr + search_dist;
	double xl_curr = x_left;
	double xl_next = xl_curr - search_dist;

	std::vector<cv::Point2d> selected_points = initial_points;

	bool check = true;
	while (check) {
		double xr_next1 = xr_next + overlap;
		double xr_curr1 = xr_curr - overlap;
		double xl_next1 = xl_next - overlap;
		double xl_curr1 = xl_curr + overlap;

		// 找到候选点
		std::vector<cv::Point2d> candidate_points;
		for (const auto& pt : points) {
			if ((pt.x <= xr_next1 && pt.x > xr_curr1) ||
				(pt.x >= xl_next1 && pt.x < xl_curr1)) {
				candidate_points.push_back(pt);
			}
		}

		if (!candidate_points.empty()) {
			std::vector<cv::Point2d> nearby_points =
				get_nearby_hor_points(selected_points, candidate_points, residual, order);

			if (!nearby_points.empty()) {
				// 合并
				selected_points.insert(selected_points.end(),
					nearby_points.begin(), nearby_points.end());
				unique_points(selected_points);
			}
			else {
				check = false;
			}
		}
		else {
			check = false;
		}

		xr_curr = xr_next;
		xr_next = xr_curr + search_dist;
		xl_curr = xl_next;
		xl_next = xl_curr - search_dist;
	}

	return selected_points;
}

std::vector<std::vector<cv::Point2d>> CodCalc::group_dots_hor_lines_based_polyfit(const std::vector<cv::Point2d>& points,
	double slope, double line_dist, double ratio /*= 0.1*/, int num_dot_miss /*= 3*/, double accepted_ratio /*= 0.65*/,
	double overlap_ratio /*= 0.5*/, int order /*= 2*/)
{
	// --- Step 1: 参数检查 ---
	int num_points = static_cast<int>(points.size());
	if (num_points == 0) {
		return {};
	}

	// --- Step 2: 旋转点集 ---
	double angle = -std::atan(slope);  // radian
	std::vector<cv::Point2d> rotated_points = rotate_points(points, angle, false);

	// 按 x 排序
	std::sort(rotated_points.begin(), rotated_points.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.x < b.x;
		});

	std::vector<double> x_list;
	x_list.reserve(rotated_points.size());
	for (auto& p : rotated_points)
		x_list.push_back(p.x);

	double x_min = x_list.front();
	double x_max = x_list.back();
	double x_mid = 0.5 * (x_min + x_max);

	// --- Step 3: 搜索范围 ---
	num_dot_miss = std::clamp(num_dot_miss, 1, num_points);
	double search_dist = num_dot_miss * line_dist + 0.5 * line_dist;
	double x_start = std::clamp(x_mid - search_dist, x_min, x_max);
	double x_stop = std::clamp(x_mid + search_dist, x_min, x_max);

	// 提取 idx_list
	std::vector<int> idx_list;
	for (int i = 0; i < (int)x_list.size(); i++) {
		if (x_list[i] >= x_start && x_list[i] <= x_stop) {
			idx_list.push_back(i);
		}
	}

	std::vector<std::vector<cv::Point2d>> list_lines;

	if (!idx_list.empty()) {
		// --- Step 4: 提取候选点 ---
		std::vector<cv::Point2d> selected_points;
		for (int idx : idx_list)
			selected_points.push_back(rotated_points[idx]);

		// --- Step 5: 先用 group_dots_hor_lines 初步分组 ---
		auto grouped_points = group_dots_hor_lines(
			selected_points, 0.0, line_dist, ratio, num_dot_miss, accepted_ratio);

		if (!grouped_points.empty()) {
			double residual = ratio * line_dist;
			for (auto& current_points : grouped_points) {
				std::vector<cv::Point2d> refined_points = current_points;
				if (refined_points.size() > 2) {
					double x_left = refined_points.front().x;
					double x_right = refined_points.back().x;

					refined_points = get_nearby_hor_points_iter(
						refined_points, rotated_points, x_left, x_right,
						search_dist, residual, overlap_ratio, order);
				}

				if (refined_points.size() > 2) {
					// 旋转回原坐标
					refined_points = rotate_points(refined_points, -angle, false);

					// 按 x 排序
					std::sort(refined_points.begin(), refined_points.end(),
						[](const cv::Point2d& a, const cv::Point2d& b) {
							return a.x < b.x;
						});

					list_lines.push_back(refined_points);
				}
			}
		}
	}

	// --- Step 6: 过滤短的线条 ---
	std::vector<int> list_len;
	for (auto& l : list_lines)
		list_len.push_back((int)l.size());

	if (list_len.empty())
		return {};

	int max_len = *std::max_element(list_len.begin(), list_len.end());
	int len_accepted = static_cast<int>(accepted_ratio * max_len);

	std::vector<std::vector<cv::Point2d>> lines_selected;
	for (auto& line : list_lines) {
		if ((int)line.size() > len_accepted) {
			lines_selected.push_back(line);
		}
	}

	// --- Step 7: y 坐标聚类过滤 ---
	std::vector<double> y_vals;
	for (auto& line : lines_selected) {
		std::vector<double> ys;
		ys.reserve(line.size());
		for (auto& p : line) ys.push_back(p.y);

		// median
		std::nth_element(ys.begin(), ys.begin() + std::ceil(ys.size() / 2.), ys.end());
		double median_y{ 0. };

		if (ys.size() % 2 == 1)
		{
			median_y = ys[ys.size() / 2]; // 中位数
		}
		else
		{
			median_y = (ys[ys.size() / 2] + ys[ys.size() / 2 - 1]) / 2;
		}
		y_vals.push_back(median_y);
	}

	std::vector<int> ids;
	for (size_t i = 1; i < y_vals.size(); i++) {
		if (std::abs(y_vals[i] - y_vals[i - 1]) > 0.1 * line_dist) {
			ids.push_back((int)i);
		}
	}

	if (!ids.empty()) {
		std::vector<int> keep_ids;
		keep_ids.push_back(0);
		for (auto id : ids)
			keep_ids.push_back(id + 1);

		std::vector<std::vector<cv::Point2d>> lines_tmp;
		for (size_t idx = 0; idx < lines_selected.size(); idx++) {
			if (std::find(keep_ids.begin(), keep_ids.end(), idx) != keep_ids.end()) {
				lines_tmp.push_back(lines_selected[idx]);
			}
		}
		lines_selected = lines_tmp;
	}

	// --- Step 8: 按平均 y 排序 ---
	std::sort(lines_selected.begin(), lines_selected.end(),
		[](const std::vector<cv::Point2d>& a,
			const std::vector<cv::Point2d>& b) {
				double ya = std::accumulate(a.begin(), a.end(), 0.0,
					[](double sum, const cv::Point2d& p) {
						return sum + p.y; }) / a.size();
						double yb = std::accumulate(b.begin(), b.end(), 0.0,
							[](double sum, const cv::Point2d& p) {
								return sum + p.y; }) / b.size();
								return ya < yb;
		});

	return lines_selected;
}

std::vector<std::vector<cv::Point2d>> CodCalc::group_dots_ver_lines(const std::vector<cv::Point2d>& list_dots, 
	double slope, double dot_dist, double ratio /*= 0.3*/, int num_dot_miss /*= 6*/, double accepted_ratio /*= 0.75*/)
{
	int num_dots = static_cast<int>(list_dots.size());
	if (num_dots == 0) {
		return {};
	}

	int num_dots_left = num_dots;
	std::vector<cv::Point2d> list_dots_left = list_dots;

	// 按 y 坐标排序
	std::sort(list_dots_left.begin(), list_dots_left.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.y < b.y;
		});

	std::vector<std::vector<cv::Point2d>> list_lines;

	// --- Step 3: 循环分组 ---
	while (num_dots_left > 1) {
		cv::Point2d dot1 = list_dots_left[0];
		std::vector<cv::Point2d> dots_selected = { dot1 };
		std::vector<int> pos_get = { 0 };

		for (int i = 1; i < (int)list_dots_left.size(); i++) {
			cv::Point2d dot2 = list_dots_left[i];
			if (check_dot_on_line_ver(dot1, dot2, slope, dot_dist, ratio, num_dot_miss)) {
				dot1 = dot2;
				dots_selected.push_back(dot2);
				pos_get.push_back(i);
			}
		}

		// 剩余点
		std::vector<cv::Point2d> new_list;
		for (int i = 0; i < (int)list_dots_left.size(); i++) {
			if (std::find(pos_get.begin(), pos_get.end(), i) == pos_get.end()) {
				new_list.push_back(list_dots_left[i]);
			}
		}
		list_dots_left.swap(new_list);
		num_dots_left = (int)list_dots_left.size();

		if (dots_selected.size() > 1) {
			list_lines.push_back(dots_selected);
		}
	}

	// --- Step 4: 过滤 ---
	std::vector<int> list_length;
	for (auto& l : list_lines) 
		list_length.push_back((int)l.size());

	if (list_length.empty())
		return {};

	int max_len = *std::max_element(list_length.begin(), list_length.end());
	int len_accepted = static_cast<int>(accepted_ratio * max_len);

	std::vector<std::vector<cv::Point2d>> lines_selected;
	for (auto& line : list_lines) {
		if ((int)line.size() > len_accepted) {
			lines_selected.push_back(line);
		}
	}

	// --- Step 5: 按 x 均值排序 ---
	std::sort(lines_selected.begin(), lines_selected.end(),
		[](const std::vector<cv::Point2d>& a,
			const std::vector<cv::Point2d>& b) {
				double mean_a = 0.0, mean_b = 0.0;
				for (auto& p : a) mean_a += p.x;
				for (auto& p : b) mean_b += p.x;
				mean_a /= a.size();
				mean_b /= b.size();
				return mean_a < mean_b;
		});

	return lines_selected;
}

std::vector<cv::Point2d> CodCalc::get_nearby_ver_points(const std::vector<cv::Point2d>& current_points, const std::vector<cv::Point2d>& points, double residual, int order /*= 2*/)
{
	// 分离 y, x  (注意: 这里拟合 x = f(y))
	std::vector<double> y, x;
	y.reserve(current_points.size());
	x.reserve(current_points.size());
	for (const auto& pt : current_points) {
		y.push_back(pt.y);
		x.push_back(pt.x);
	}

	// 多项式拟合
	Eigen::VectorXd coeffs = polyfit(y, x, order);

	// 遍历 points，计算和拟合曲线的距离
	std::vector<cv::Point2d> nearby_points;
	for (const auto& pt : points) {
		double pred_x = polyval(coeffs, pt.y);
		double dist = std::abs(pt.x - pred_x);
		if (dist <= residual) {
			nearby_points.push_back(pt);
		}
	}

	return nearby_points;
}

std::vector<cv::Point2d> CodCalc::get_nearby_ver_points_iter(const std::vector<cv::Point2d>& initial_points, const std::vector<cv::Point2d>& points, double y_left, double y_right, double search_dist, double residual, double overlap_ratio /*= 0.5*/,int order /*= 2*/)
{
	// 限制 overlap_ratio 在 [0,1]
	overlap_ratio = std::max(0.0, std::min(1.0, overlap_ratio));
	double overlap = search_dist * overlap_ratio;

	double yr_curr = y_right;
	double yr_next = yr_curr + search_dist;
	double yl_curr = y_left;
	double yl_next = yl_curr - search_dist;

	std::vector<cv::Point2d> selected_points = initial_points;
	bool check = true;

	while (check) {
		double yr_next1 = yr_next + overlap;
		double yr_curr1 = yr_curr - overlap;
		double yl_next1 = yl_next - overlap;
		double yl_curr1 = yl_curr + overlap;

		// 找到在当前纵向范围内的候选点
		std::vector<cv::Point2d> candidate_points;
		for (const auto& pt : points) {
			if (((pt.y <= yr_next1) && (pt.y > yr_curr1)) ||
				((pt.y >= yl_next1) && (pt.y < yl_curr1))) {
				candidate_points.push_back(pt);
			}
		}

		if (!candidate_points.empty()) {
			// 使用拟合检查
			std::vector<cv::Point2d> nearby_points =
				get_nearby_ver_points(selected_points, candidate_points, residual, order);

			if (!nearby_points.empty()) {
				// 合并新点
				selected_points.insert(selected_points.end(), nearby_points.begin(), nearby_points.end());
				unique_points(selected_points);

			}
			else {
				check = false;
			}
		}
		else {
			check = false;
		}

		// 更新搜索区间
		yr_curr = yr_next;
		yr_next = yr_curr + search_dist;
		yl_curr = yl_next;
		yl_next = yl_curr - search_dist;
	}

	return selected_points;
}

std::vector<std::vector<cv::Point2d>> CodCalc::group_dots_ver_lines_based_polyfit(const std::vector<cv::Point2d>& points,
	double slope, double line_dist, double ratio /*= 0.1*/, int num_dot_miss /*= 3*/, double accepted_ratio /*= 0.65*/,
	double overlap_ratio /*= 0.5*/, int order /*= 2*/)
{
	double angle = std::atan(slope);  // 与Python一致
	int num_points = static_cast<int>(points.size());
	if (num_points == 0)
	{
		return {};
	}

	// 旋转点集 (degree_unit = false)
	auto rotated_points = rotate_points(points, angle, false);

	// 按 y 排序
	std::sort(rotated_points.begin(), rotated_points.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.y < b.y;
		});

	std::vector<double> y_list;
	y_list.reserve(rotated_points.size());
	for (const auto& p : rotated_points)
		y_list.push_back(p.y);

	double y_min = y_list.front();
	double y_max = y_list.back();
	double y_mid = 0.5 * (y_min + y_max);

	num_dot_miss = std::max(1, std::min(num_dot_miss, num_points));
	double search_dist = num_dot_miss * line_dist + 0.5 * line_dist;

	double y_start = std::clamp(y_mid - search_dist, y_min, y_max);
	double y_stop = std::clamp(y_mid + search_dist, y_min, y_max);

	// 找到在 y 范围内的点
	std::vector<cv::Point2d> selected_points;
	for (const auto& p : rotated_points)
	{
		if (p.y >= y_start && p.y <= y_stop)
			selected_points.push_back(p);
	}

	std::vector<std::vector<cv::Point2d>> list_lines;

	if (!selected_points.empty())
	{
		// 初步分组
		auto grouped_points = group_dots_ver_lines(
			selected_points, 0.0, line_dist, ratio, num_dot_miss, accepted_ratio);

		if (!grouped_points.empty())
		{
			double residual = ratio * line_dist;

			for (auto& current_points : grouped_points)
			{
				std::vector<cv::Point2d> refined_points = current_points;

				if (refined_points.size() > 2)
				{
					double y_left = refined_points.front().y;
					double y_right = refined_points.back().y;

					refined_points = get_nearby_ver_points_iter(
						refined_points, rotated_points,
						y_left, y_right,
						search_dist, residual, overlap_ratio, order);
				}

				if (refined_points.size() > 2)
				{
					// 反向旋转
					refined_points = rotate_points(refined_points, -angle, false);

					// 按 y 排序
					std::sort(refined_points.begin(), refined_points.end(),
						[](const cv::Point2d& a, const cv::Point2d& b) {
							return a.y < b.y;
						});

					list_lines.push_back(refined_points);
				}
			}
		}
	}

	// 过滤太短的线
	std::vector<int> list_len;
	list_len.reserve(list_lines.size());
	for (const auto& line : list_lines) list_len.push_back((int)line.size());

	std::vector<std::vector<cv::Point2d>> lines_selected;
	if (!list_len.empty())
	{
		int max_len = *std::max_element(list_len.begin(), list_len.end());
		int len_accepted = static_cast<int>(accepted_ratio * max_len);

		for (const auto& line : list_lines)
		{
			if ((int)line.size() > len_accepted)
				lines_selected.push_back(line);
		}
	}

	// 根据中值 X 过滤重叠的线
	std::vector<double> x_vals;
	x_vals.reserve(lines_selected.size());
	for (const auto& line : lines_selected)
	{
		std::vector<double> xs;
		for (const auto& p : line) xs.push_back(p.x);
		std::nth_element(xs.begin(), xs.begin() + std::ceil(xs.size() / 2.0), xs.end());

		if (xs.size() % 2 == 1)
		{
			x_vals.push_back(xs[xs.size() / 2]);  // median
		}
		else
		{
			x_vals.push_back((xs[xs.size() / 2] + xs[xs.size() / 2 - 1]) / 2);
		}



	}

	std::vector<int> ids;
	for (size_t i = 1; i < x_vals.size(); ++i)
	{
		if (std::abs(x_vals[i] - x_vals[i - 1]) > 0.1 * line_dist)
		{
			ids.push_back((int)i);
		}
	}

	if (!ids.empty())
	{
		std::vector<int> keep_ids;
		keep_ids.push_back(0);
		for (auto id : ids)
			keep_ids.push_back(id + 1);

		std::vector<std::vector<cv::Point2d>> lines_tmp;
		for (size_t idx = 0; idx < lines_selected.size(); idx++) {
			if (std::find(keep_ids.begin(), keep_ids.end(), idx) != keep_ids.end()) {
				lines_tmp.push_back(lines_selected[idx]);
			}
		}
		lines_selected = lines_tmp;
	}

	// 按平均 X 排序
	std::sort(lines_selected.begin(), lines_selected.end(),
		[](const std::vector<cv::Point2d>& a,
			const std::vector<cv::Point2d>& b) {
				double mean_a = std::accumulate(a.begin(), a.end(), 0.0,
					[](double s, const cv::Point2d& p) { return s + p.x; }) / a.size();
				double mean_b = std::accumulate(b.begin(), b.end(), 0.0,
					[](double s, const cv::Point2d& p) { return s + p.x; }) / b.size();
				return mean_a < mean_b;
		});

	return lines_selected;
}

std::vector<std::vector<cv::Point2d>> CodCalc::remove_residual_dots_hor(const std::vector<std::vector<cv::Point2d>>& list_lines,
	double slope, double residual /*= 2.5*/)
{
	std::vector<std::vector<cv::Point2d>> list_lines2;

	for (const auto& line : list_lines) {
		if (line.size() < 3) continue;  // 二次多项式至少需要3个点

		int N = static_cast<int>(line.size());

		// 构造拟合矩阵 A 和向量 b
		Eigen::MatrixXd A(N, 3);
		Eigen::VectorXd b(N);

		for (int i = 0; i < N; i++) {
			double x = line[i].x;  // 横坐标
			double y = line[i].y;  // 纵坐标
			A(i, 0) = x * x;
			A(i, 1) = x;
			A(i, 2) = 1.0;
			b(i) = y;
		}

		// 最小二乘求解 [a2, a1, a0]
		Eigen::Vector3d coeffs = A.colPivHouseholderQr().solve(b);
		double a2 = coeffs(0), a1 = coeffs(1), a0 = coeffs(2);

		// 计算残差
		std::vector<cv::Point2d> dots_left;
		dots_left.reserve(N);

		double cos_theta = std::cos(std::atan(slope));
		for (int i = 0; i < N; i++) {
			double x = line[i].x;
			double y = line[i].y;
			double y_fit = a2 * x * x + a1 * x + a0;
			double err = std::abs((y_fit - y) * cos_theta);
			if (err < residual) {
				dots_left.push_back(line[i]);
			}
		}

		if (!dots_left.empty()) {
			list_lines2.push_back(dots_left);
		}
	}


	return list_lines2;
}

std::vector<std::vector<cv::Point2d>> CodCalc::remove_residual_dots_ver(const std::vector<std::vector<cv::Point2d>>& list_lines,
	double slope, double residual /*= 2.5*/)
{
	std::vector<std::vector<cv::Point2d>> list_lines2;

	for (const auto& line : list_lines)
	{
		if (line.size() < 3) continue; // 至少需要3个点才能拟合二次多项式

		// 相当于 np.fliplr: (y, x) -> (x, y)
		std::vector<double> X, Y;
		X.reserve(line.size());
		Y.reserve(line.size());
		for (const auto& p : line)
		{
			X.push_back(p.y);
			Y.push_back(p.x);
		}

		// 二次多项式拟合
		Eigen::VectorXd coeff = polyfit(X, Y, 2);  // coeff = [a0, a1, a2]

		// 计算误差
		std::vector<double> error(line.size());
		for (size_t i = 0; i < line.size(); ++i)
		{
			double y_fit = polyval(coeff, X[i]);  // 拟合值
			error[i] = std::abs((y_fit - Y[i]) * std::cos(std::atan(slope)));
		}

		// 过滤掉超出 residual 的点
		std::vector<cv::Point2d> dots_left;
		for (size_t i = 0; i < line.size(); ++i)
		{
			if (error[i] < residual)
			{
				// 交换回来: (x, y) -> (y, x)
				dots_left.emplace_back(Y[i], X[i]);
			}
		}

		if (!dots_left.empty())
		{
			list_lines2.push_back(dots_left);
		}
	}


	return list_lines2;
}

std::pair<std::vector<Eigen::VectorXd>, std::vector<std::vector<cv::Point2d>>> CodCalc::_para_fit_hor(
	const std::vector<std::vector<cv::Point2d>>& list_lines, double xcenter, double ycenter)
{
	int num_line = static_cast<int>(list_lines.size());

	// 存储每条线的二次多项式系数 (a, b, c)
	std::vector<Eigen::VectorXd> list_coef(num_line);

	// 存储每条线平移后的点
	std::vector<std::vector<cv::Point2d>> list_slines;
	list_slines.reserve(num_line);

	for (int i = 0; i < num_line; ++i)
	{
		const auto& iline = list_lines[i];
		std::vector<double> x, y;
		x.reserve(iline.size());
		y.reserve(iline.size());

		// 提取并平移坐标 (注意: Python 是 (y, x))
		for (const auto& pt : iline)
		{
			x.push_back(pt.x - xcenter);
			y.push_back(pt.y - ycenter);
		}

		// 拟合二次多项式: y = a*x^2 + b*x + c
		Eigen::VectorXd coeff = polyfit(x, y, 2);

		// 保存结果 (长度为3: [a, b, c])
		list_coef[i] = coeff;

		// 保存平移后的点
		std::vector<cv::Point2d> shifted;
		shifted.reserve(iline.size());
		for (const auto& pt : iline)
		{
			shifted.emplace_back(pt.x - xcenter, pt.y - ycenter);
		}
		list_slines.push_back(std::move(shifted));
	}

	return { list_coef, list_slines };
}

std::pair<std::vector<Eigen::VectorXd>, std::vector<std::vector<cv::Point2d>>> CodCalc::_para_fit_ver(
	const std::vector<std::vector<cv::Point2d>>& list_lines, double xcenter, double ycenter)
{
	int num_line = static_cast<int>(list_lines.size());

	// 存储每条线的二次多项式系数 (a, b, c)
	std::vector<Eigen::VectorXd> list_coef(num_line);

	// 存储每条线平移后的点
	std::vector<std::vector<cv::Point2d>> list_slines;
	list_slines.reserve(num_line);

	for (int i = 0; i < num_line; ++i)
	{
		const auto& iline = list_lines[i];
		std::vector<double> x, y;
		x.reserve(iline.size());
		y.reserve(iline.size());

		for (const auto& pt : iline)
		{
			x.push_back(pt.y - ycenter);
			y.push_back(pt.x - xcenter);
		}

		// 拟合二次多项式: y = a*x^2 + b*x + c
		Eigen::VectorXd coeff = polyfit(x, y, 2);

		// 保存结果 (长度为3: [a, b, c])
		list_coef[i] = coeff;

		// 保存平移后的点
		std::vector<cv::Point2d> shifted;
		shifted.reserve(iline.size());
		for (const auto& pt : iline)
		{
			shifted.emplace_back(pt.x - xcenter, pt.y - ycenter);
		}
		list_slines.push_back(std::move(shifted));
	}

	return { list_coef, list_slines };
}

double CodCalc::median(std::vector<double> vec)
{
	if (vec.empty()) return 0.0;
	std::sort(vec.begin(), vec.end());
	size_t n = vec.size();
	if (n % 2 == 0) {
		return 0.5 * (vec[n / 2 - 1] + vec[n / 2]);
	}
	else {
		return vec[n / 2];
	}
}

std::pair<double, double> CodCalc::__get_representative_linear_coefs(const std::vector<Eigen::VectorXd>& list_coefs,
	const std::vector<int>& indices, const std::string& method)
{
	std::vector<double> a_vals, b_vals, c_vals;
	a_vals.reserve(indices.size());
	b_vals.reserve(indices.size());
	c_vals.reserve(indices.size());

	for (int idx : indices) {
		a_vals.push_back(list_coefs[idx][2]); // a
		b_vals.push_back(list_coefs[idx][1]); // b
		c_vals.push_back(list_coefs[idx][0]); // c
	}

	double b_val = 0.0, c_val = 0.0;

	if (method == "median") {
		b_val = median(b_vals);
		c_val = median(c_vals);
	}
	else if (method == "max" || method == "min") {
		// list_tmp = [b, c, a]
		struct Entry {
			double a, b, c;
		};
		std::vector<Entry> entries;
		entries.reserve(indices.size());
		for (size_t i = 0; i < indices.size(); ++i) {
			entries.push_back({ a_vals[i], b_vals[i], c_vals[i] });
		}

		// 按 a 排序
		std::sort(entries.begin(), entries.end(),
			[](const Entry& e1, const Entry& e2) {
				return e1.a < e2.a;
			});

		if (method == "max") {
			if (!entries.empty() && entries.front().a > 0) {
				b_val = entries.back().b;
				c_val = entries.back().c;
			}
			else {
				b_val = entries.front().b;
				c_val = entries.front().c;
			}
		}
		else { // method == "min"
			if (!entries.empty() && entries.front().a > 0) {
				b_val = entries.front().b;
				c_val = entries.front().c;
			}
			else {
				b_val = entries.back().b;
				c_val = entries.back().c;
			}
		}
	}
	else {
		// mean
		double b_sum = std::accumulate(b_vals.begin(), b_vals.end(), 0.0);
		double c_sum = std::accumulate(c_vals.begin(), c_vals.end(), 0.0);
		b_val = b_sum / b_vals.size();
		c_val = c_sum / c_vals.size();
	}

	return { b_val, c_val };
}

std::pair<double, double> CodCalc::_find_cross_point_between_lines(
	const Eigen::Vector2d& line_coef_hor,/* [a1, b1] y = a1*x + b1 */
	const Eigen::Vector2d& line_coef_ver) // [a2, b2] x = a2*y + b2
{
	double a1 = line_coef_hor(0);
	double b1 = line_coef_hor(1);
	double a2 = line_coef_ver(0);
	double b2 = line_coef_ver(1);

	double denom = 1.0 - a1 * a2;
	double y = (a1 * b2 + b1) / denom;
	double x = a2 * y + b2;
	return { x, y };
}

std::pair<std::vector<cv::Point2d>, std::vector<cv::Point2d>> CodCalc::generate_4_source_target_perspective_points(
	const std::vector<cv::Point2d>& points_in, bool equal_dist /*= false*/, 
	const std::string& scale_mode /*= "mean"*/, double scale_value /*= 1.0*/) // 仅当 scale_mode 不是 "mean/min/max" 时才用
{
	if (points_in.size() != 4) {
		return { std::vector<cv::Point2d>(), std::vector<cv::Point2d>() };
	}

	// 拷贝一份输入点
	std::vector<cv::Point2d> points = points_in;

	// 按 y 排序
	std::sort(points.begin(), points.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.y < b.y;
		});

	// 上两个点 (p12)，再按 x 排序
	std::vector<cv::Point2d> p12(points.begin(), points.begin() + 2);
	std::sort(p12.begin(), p12.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.x < b.x;
		});
	cv::Point2d P1 = p12[0];
	cv::Point2d P2 = p12[1];

	// 下两个点 (p34)，再按 x 排序
	std::vector<cv::Point2d> p34(points.begin() + 2, points.begin() + 4);
	std::sort(p34.begin(), p34.end(),
		[](const cv::Point2d& a, const cv::Point2d& b) {
			return a.x < b.x;
		});
	cv::Point2d P3 = p34[0];
	cv::Point2d P4 = p34[1];

	std::vector<cv::Point2d> source_points = { P1, P2, P3, P4 };

	// 拟合直线
	double a12 = (P1.y - P2.y) / (P1.x - P2.x);
	double b12 = P1.y - a12 * P1.x;
	double a34 = (P3.y - P4.y) / (P3.x - P4.x);
	double b34 = P3.y - a34 * P3.x;
	double ah = 0.5 * (a12 + a34);
	double bh = 0.5 * (b12 + b34);

	double a13 = (P1.x - P3.x) / (P1.y - P3.y);
	double b13 = P1.x - a13 * P1.y;
	double a24 = (P2.x - P4.x) / (P2.y - P4.y);
	double b24 = P2.x - a24 * P2.y;
	double av = 0.5 * (a13 + a24);
	double bv = 0.5 * (b13 + b24);

	double a0 = (ah >= 0 ? 1.0 : -1.0) * (std::abs(ah) + std::abs(av)) * 0.5;

	// 计算边长
	double dist12 = cv::norm(P1 - P2);
	double dist13 = cv::norm(P1 - P3);
	double dist24 = cv::norm(P2 - P4);
	double dist34 = cv::norm(P3 - P4);

	double dist_h, dist_v;
	if (scale_mode == "max") {
		dist_h = std::max(dist12, dist34);
		dist_v = std::max(dist13, dist24);
		if (equal_dist)
			dist_h = dist_v = std::max(dist_h, dist_v);
	}
	else if (scale_mode == "min") {
		dist_h = std::min(dist12, dist34);
		dist_v = std::min(dist13, dist24);
		if (equal_dist)
			dist_h = dist_v = std::min(dist_h, dist_v);
	}
	else {  // "mean" 或 float
		dist_h = 0.5 * (dist12 + dist34);
		dist_v = 0.5 * (dist13 + dist24);
		if (scale_mode != "mean") {
			dist_h *= scale_value;
			dist_v *= scale_value;
		}
		if (equal_dist)
			dist_h = dist_v = 0.5 * (dist_h + dist_v);
	}

	dist_h *= 0.5;
	dist_v *= 0.5;

	// 构造目标矩形
	double b1 = bh - std::abs(dist_v / std::cos(std::atan(a0)));
	double b2 = bh + std::abs(dist_v / std::cos(std::atan(a0)));
	double b3 = bv - std::abs(dist_h / std::cos(std::atan(a0)));
	double b4 = bv + std::abs(dist_h / std::cos(std::atan(a0)));

	double y1 = (a0 * b3 + b1) / (1.0 + a0 * a0);
	double x1 = -a0 * y1 + b3;
	double y2 = (a0 * b4 + b1) / (1.0 + a0 * a0);
	double x2 = -a0 * y2 + b4;
	double y3 = (a0 * b3 + b2) / (1.0 + a0 * a0);
	double x3 = -a0 * y3 + b3;
	double y4 = (a0 * b4 + b2) / (1.0 + a0 * a0);
	double x4 = -a0 * y4 + b4;

	std::vector<cv::Point2d> target_points = {
		{x1, y1}, {x2, y2}, {x3, y3}, {x4, y4}
	};

	// 注意：Python 里 target_points 是 [[y,x]] 格式
	// 这里已经按照 (x,y) 存储 cv::Point2d，使用时保持一致

	return { source_points, target_points };
}

std::vector<double> CodCalc::_find_cross_point_between_parabolas_same_direction(const Eigen::Vector3d& para_coef1, 
	const Eigen::Vector3d& para_coef2)
{
	double a = para_coef1[0] - para_coef2[0];
	double b = para_coef1[1] - para_coef2[1];
	double c = para_coef1[2] - para_coef2[2];

	// 无效情况
	if (std::abs(a) < 1e-12 && std::abs(b) < 1e-12) {
		return {0, 0}; // 完全重合或平行，不符合“两交点”假设
	}

	// 解二次方程
	std::vector<double> results;
	if (std::abs(a) < 1e-12) {
		// 退化为一次方程
		double x = -c / b;
		results.push_back(x);
	}
	else {
		double D = b * b - 4 * a * c;
		if (D < 0) {
			return { }; // 复数解
		}
		else if (std::abs(D) < 1e-12) {
			double x = -b / (2 * a);
			results.push_back(x);
		}
		else {
			double sqrtD = std::sqrt(D);
			double x1 = (-b + sqrtD) / (2 * a);
			double x2 = (-b - sqrtD) / (2 * a);
			results.push_back(x1);
			results.push_back(x2);
		}
	}

	return results;
}

std::pair<double, double> CodCalc::find_cod_coarse(const std::vector<std::vector<cv::Point2d>>& list_hor_lines, 
	const std::vector<std::vector<cv::Point2d>>& list_ver_lines)
{
	// step 1: 拟合水平线、垂直线
	auto [list_coef_hor, shifted_hor] = _para_fit_hor(list_hor_lines, 0.0, 0.0);
	auto [list_coef_ver, shifted_ver] = _para_fit_ver(list_ver_lines, 0.0, 0.0);

	int num_hor = static_cast<int>(list_coef_hor.size());
	int num_ver = static_cast<int>(list_coef_ver.size());

	// 转换为 Eigen 矩阵方便取列
	Eigen::MatrixXd coef_hor(num_hor, 3);
	for (int i = 0; i < num_hor; ++i)
		coef_hor.row(i) = list_coef_hor[i].transpose();

	Eigen::MatrixXd coef_ver(num_ver, 3);
	for (int i = 0; i < num_ver; ++i)
		coef_ver.row(i) = list_coef_ver[i].transpose();

	// step 2: 找到 a 系数符号变化的位置
	int pos_hor = -1, pos_ver = -1;
	for (int i = 0; i < num_hor - 1; ++i) {
		if ((coef_hor(i, 2) >= 0 && coef_hor(i + 1, 2) < 0) ||
			(coef_hor(i, 2) < 0 && coef_hor(i + 1, 2) >= 0)) {
			pos_hor = i + 1;
			break;
		}
	}
	for (int i = 0; i < num_ver - 1; ++i) {
		if ((coef_ver(i, 2) >= 0 && coef_ver(i + 1, 2) < 0) ||
			(coef_ver(i, 2) < 0 && coef_ver(i + 1, 2) >= 0)) {
			pos_ver = i + 1;
			break;
		}
	}

	if (pos_hor <= 0 || pos_ver <= 0) {
		// 没有找到符号变化 → 返回 (0,0) 或者抛异常
		return { 0.0, 0.0 };
	}

	// step 3: 计算近似中心
	double ycenter0 = 0.5 * (coef_hor(pos_hor - 1, 2) + coef_hor(pos_hor, 2));
	double xcenter0 = 0.5 * (coef_ver(pos_ver - 1, 2) + coef_ver(pos_ver, 2));

	double slope_hor = 0.5 * (coef_hor(pos_hor - 1, 1) + coef_hor(pos_hor, 1));
	double slope_ver = 0.5 * (coef_ver(pos_ver - 1, 1) + coef_ver(pos_ver, 1));

	double denom = 1.0 - slope_hor * slope_ver;
	if (std::abs(denom) < 1e-12) denom = 1e-12; // 防止除零

	double ycenter = (ycenter0 + xcenter0 * slope_hor) / denom;
	double xcenter = (xcenter0 + ycenter0 * slope_ver) / denom;

	return { xcenter, ycenter };
}

std::pair<double, double> CodCalc::find_cod_bailey(const std::vector<std::vector<cv::Point2d>>& list_hor_lines, 
	const std::vector<std::vector<cv::Point2d>>& list_ver_lines, int iteration /*= 2*/)
{
	// --- 1. 初始粗估计 ---
	auto [xcenter, ycenter] = find_cod_coarse(list_hor_lines, list_ver_lines);

	for (int it = 0; it <= iteration; ++it) {
		// --- 2. 拟合所有水平 & 垂直线 ---
		auto [list_coef_hor, _hor] = _para_fit_hor(list_hor_lines, xcenter, ycenter);
		auto [list_coef_ver, _ver] = _para_fit_ver(list_ver_lines, xcenter, ycenter);

		// --- 3. 提取数据：c → a ---
		std::vector<double> x_hor, y_hor;
		for (const auto& coef : list_coef_hor) {
			x_hor.push_back(coef(0)); // c
			y_hor.push_back(coef(2)); // a
		}
		std::vector<double> x_ver, y_ver;
		for (const auto& coef : list_coef_ver) {
			x_ver.push_back(coef(0)); // c
			y_ver.push_back(coef(2)); // a
		}

		// --- 4. 线性拟合 ---
		Eigen::VectorXd fit_hor = polyfit(x_hor, y_hor, 1); // y = a1*x + b1
		Eigen::VectorXd fit_ver = polyfit(x_ver, y_ver, 1); // y = a2*x + b2

		double a1 = fit_hor(1); // slope
		double b1 = fit_hor(0); // intercept
		double a2 = fit_ver(1);
		double b2 = fit_ver(0);

		// --- 5. 更新中心 ---
		xcenter = xcenter - b2 / a2;
		ycenter = ycenter - b1 / a1;
	}

	return { xcenter, ycenter };
}

std::pair<double, double> CodCalc::_find_center_based_vanishing_points_2nd_way(
	const std::vector<std::vector<cv::Point2d>>& list_hor_lines_in, const std::vector<std::vector<cv::Point2d>>& list_ver_lines_in)
{
	// 对水平/垂直线分别做抛物线拟合，初始 (xcenter, ycenter) = (0, 0)
	auto hor_fit = _para_fit_hor(list_hor_lines_in, 0.0, 0.0);
	auto ver_fit = _para_fit_ver(list_ver_lines_in, 0.0, 0.0);

	const std::vector<Eigen::VectorXd>& list_coef_hor = hor_fit.first;  // 每项为 [a, b, c]^T（期望长度>=3）
	const std::vector<std::vector<cv::Point2d>>& list_hor_lines = hor_fit.second;

	const std::vector<Eigen::VectorXd>& list_coef_ver = ver_fit.first;  // 每项为 [a, b, c]^T（期望长度>=3）
	const std::vector<std::vector<cv::Point2d>>& list_ver_lines = ver_fit.second;

	// ---------- 水平方向：找曲率最小（|a| 最小）的那条抛物线，与其它抛物线两两求交点 ----------
	std::vector<cv::Point2d> xy_hlist; // 收集形如 (x, y) 的点

	if (!list_coef_hor.empty()) {
		int pos_min = -1;
		double best = std::numeric_limits<double>::infinity();
		for (int i = 0; i < static_cast<int>(list_coef_hor.size()); ++i) {
			if (list_coef_hor[i].size() >= 1) {
				double aabs = std::abs(list_coef_hor[i][2]);
				if (aabs < best) {
					best = aabs;
					pos_min = i;
				}
			}
		}

		if (pos_min >= 0) {
			for (int i = 0; i < static_cast<int>(list_coef_hor.size()); ++i) {
				if (i == pos_min)
					continue;
				// 取两条抛物线系数
				const Eigen::VectorXd& ci = list_coef_hor[i];
				const Eigen::VectorXd& c0 = list_coef_hor[pos_min];
				if (ci.size() < 3 || c0.size() < 3)
					continue;

				Eigen::Vector3d p1(ci(2), ci(1), ci(0));  // y = a1 x^2 + b1 x + c1
				Eigen::Vector3d p2(c0(2), c0(1), c0(0));  // y = a2 x^2 + b2 x + c2

				std::vector<double> roots = _find_cross_point_between_parabolas_same_direction(p1, p2);
				if (roots.size() == 2) {
					double x1 = roots[0], x2 = roots[1];
					double a1 = p1(0), b1 = p1(1), c1 = p1(2);
					double a2 = p2(0), b2 = p2(1), c2 = p2(2);
					double y1 = a1 * x1 * x1 + b1 * x1 + c1;
					double y2 = a2 * x2 * x2 + b2 * x2 + c2;
					double y3 = a1 * x2 * x2 + b1 * x2 + c1;
					xy_hlist.emplace_back(x1, y1);
					xy_hlist.emplace_back(x2, y2);
				}
			}
		}
	}

	double xcenter = 0.0, ycenter = 0.0;

	if (xy_hlist.size() > 2) {
		// ---------- 垂直方向 ----------
		std::vector<cv::Point2d> xy_vlist; // 收集形如 (x, y) 的点（此处 y 是自变量）

		if (!list_coef_ver.empty()) {
			// argmin |a|
			int pos_min = -1;
			double best = std::numeric_limits<double>::infinity();
			for (int i = 0; i < static_cast<int>(list_coef_ver.size()); ++i) {
				if (list_coef_ver[i].size() >= 1) {
					double aabs = std::abs(list_coef_ver[i][2]);
					if (aabs < best) {
						best = aabs;
						pos_min = i;
					}
				}
			}
			if (pos_min >= 0) {
				for (int i = 0; i < static_cast<int>(list_coef_ver.size()); ++i) {
					if (i == pos_min) continue;
					const Eigen::VectorXd& ci = list_coef_ver[i];
					const Eigen::VectorXd& c0 = list_coef_ver[pos_min];
					if (ci.size() < 3 || c0.size() < 3) continue;

					// 这里抛物线是 x = a*y^2 + b*y + c，交点函数仍返回自变量的两个解
					Eigen::Vector3d p1(ci(2), ci(1), ci(0));  // x = a1 y^2 + b1 y + c1
					Eigen::Vector3d p2(c0(2), c0(1), c0(0));  // x = a2 y^2 + b2 y + c2

					std::vector<double> roots = _find_cross_point_between_parabolas_same_direction(p1, p2);
					if (roots.size() == 2) {
						double y1 = roots[0], y2 = roots[1];
						double a1 = p1(0), b1 = p1(1), c1 = p1(2);
						double a2 = p2(0), b2 = p2(1), c2 = p2(2);
						double x1 = a1 * y1 * y1 + b1 * y1 + c1;
						double x2 = a2 * y2 * y2 + b2 * y2 + c2;
						xy_vlist.emplace_back(x1, y1);
						xy_vlist.emplace_back(x2, y2);
					}
				}
			}
		}

		if (xy_vlist.size() > 2) {
			// ---------- 分别做两条直线拟合 ----------
			// 1) 对 xy_hlist 拟合 y = a1 * x + b1
			std::vector<double> xs_h, ys_h;
			xs_h.reserve(xy_hlist.size());
			ys_h.reserve(xy_hlist.size());
			for (const auto& pt : xy_hlist) { xs_h.push_back(pt.x); ys_h.push_back(pt.y); }
			Eigen::VectorXd coeff_h = polyfit(xs_h, ys_h, 1); // [b1, a1]（升幂）
			double a1 = coeff_h.size() >= 2 ? coeff_h(1) : 0.0; // 斜率
			double b1 = coeff_h.size() >= 1 ? coeff_h(0) : 0.0; // 截距

			// 2) 对 xy_vlist 拟合 x = a2 * y + b2  => 自变量是 y，因变量是 x
			std::vector<double> ys_v, xs_v;
			ys_v.reserve(xy_vlist.size());
			xs_v.reserve(xy_vlist.size());
			for (const auto& pt : xy_vlist) { ys_v.push_back(pt.y); xs_v.push_back(pt.x); }
			Eigen::VectorXd coeff_v = polyfit(ys_v, xs_v, 1); // [b2, a2]（升幂）
			double a2 = coeff_v.size() >= 2 ? coeff_v(1) : 0.0; // 斜率
			double b2 = coeff_v.size() >= 1 ? coeff_v(0) : 0.0; // 截距

			// 3) 两条直线的交点
			// y = a1*x + b1
			// x = a2*y + b2  ->  代入： y = a1*(a2*y + b2) + b1 = a1*a2*y + a1*b2 + b1
			// => (1 - a1*a2) * y = a1*b2 + b1
			double denom = 1.0 - a1 * a2;
			if (std::abs(denom) < 1e-12) {
				// 极罕见退化，回退到 bailey
				return find_cod_bailey(list_hor_lines, list_ver_lines, 2);
			}
			ycenter = (a1 * b2 + b1) / denom;
			xcenter = a2 * ycenter + b2;
		}
		else {
			// 垂直方向交点不足，退回 Bailey 方法
			std::tie(xcenter, ycenter) = find_cod_bailey(list_hor_lines, list_ver_lines, 2);
		}
	}
	else {
		// 水平方向交点不足，退回 Bailey 方法
		std::tie(xcenter, ycenter) = find_cod_bailey(list_hor_lines, list_ver_lines, 2);
	}

	return { xcenter, ycenter };
}

Eigen::VectorXd CodCalc::calc_perspective_coefficients(const std::vector<cv::Point2d>& source_points,
	const std::vector<cv::Point2d>& target_points, const std::string& mapping /*= "backward"*/)
{
	if (source_points.size() != 4 || target_points.size() != 4) {
		return Eigen::VectorXd();
	}

	// s_points, t_points 为 (x, y)，Python里 np.fliplr 等价于交换 x,y
	std::vector<cv::Point2d> s_points(4), t_points(4);
	if (mapping == "forward")
	{
		s_points = source_points;
		t_points = target_points;
	}
	else { // backward
		s_points = target_points;
		t_points = source_points;
	}

	// 构建 A 矩阵 (8x8)，B 矩阵 (8x1)
	Eigen::MatrixXd A(8, 8);
	Eigen::VectorXd B(8);

	for (int i = 0; i < 4; i++) {
		double x = s_points[i].x; // 对应 Python 里的 p1[0]
		double y = s_points[i].y; // 对应 Python 里的 p1[1]
		double u = t_points[i].x; // 对应 Python 里的 p2[0]
		double v = t_points[i].y; // 对应 Python 里的 p2[1]

		// 对应 Python 里 Amatrix.append(...)
		A(2 * i, 0) = x;
		A(2 * i, 1) = y;
		A(2 * i, 2) = 1.0;
		A(2 * i, 3) = 0.0;
		A(2 * i, 4) = 0.0;
		A(2 * i, 5) = 0.0;
		A(2 * i, 6) = -u * x;
		A(2 * i, 7) = -u * y;

		A(2 * i + 1, 0) = 0.0;
		A(2 * i + 1, 1) = 0.0;
		A(2 * i + 1, 2) = 0.0;
		A(2 * i + 1, 3) = x;
		A(2 * i + 1, 4) = y;
		A(2 * i + 1, 5) = 1.0;
		A(2 * i + 1, 6) = -v * x;
		A(2 * i + 1, 7) = -v * y;

		// 对应 Python 里 Bmatrix
		B(2 * i) = u;
		B(2 * i + 1) = v;
	}

	// 最小二乘解
	Eigen::VectorXd coef = A.colPivHouseholderQr().solve(B);

	// 返回结果 (8 个系数)
	return coef;
}

std::vector<std::vector<cv::Point2d>> CodCalc::correct_perspective_line(
	const std::vector<std::vector<cv::Point2d>>& list_lines, const Eigen::VectorXd& list_coef)
{
	if (list_coef.size() != 8) {
		return {};
	}

	// 提取系数
	double c1 = list_coef(0);
	double c2 = list_coef(1);
	double c3 = list_coef(2);
	double c4 = list_coef(3);
	double c5 = list_coef(4);
	double c6 = list_coef(5);
	double c7 = list_coef(6);
	double c8 = list_coef(7);

	std::vector<std::vector<cv::Point2d>> list_clines;
	list_clines.reserve(list_lines.size());

	for (const auto& iline : list_lines) {
		std::vector<cv::Point2d> corrected_line;
		corrected_line.reserve(iline.size());

		for (const auto& pt : iline) {
			// 注意：Python 中点是 (y, x)，C++ 中 cv::Point2d 是 (x, y)
			double y = pt.y;
			double x = pt.x;

			double denom = c7 * x + c8 * y + 1.0;
			double xn = (c1 * x + c2 * y + c3) / denom;
			double yn = (c4 * x + c5 * y + c6) / denom;

			corrected_line.emplace_back(xn, yn);  // C++ 的 Point2d 是 (x, y)
		}
		list_clines.push_back(std::move(corrected_line));
	}

	return list_clines;
}

std::vector<std::vector<cv::Point2d>> CodCalc::update_center(
	const std::vector<std::vector<cv::Point2d>>& list_lines, double xcenter, double ycenter)
{
	std::vector<std::vector<cv::Point2d>> updated_lines;
	updated_lines.reserve(list_lines.size());

	for (const auto& iline : list_lines)
	{
		std::vector<cv::Point2d> list_temp;
		list_temp.reserve(iline.size());

		for (const auto& point : iline)
		{
			// 注意：Python 中 (y, x)，即 point[0] = y, point[1] = x
			// cv::Point2d 是 (x, y)，所以要对应映射
			double new_y = point.y + ycenter;
			double new_x = point.x + xcenter;
			list_temp.emplace_back(new_x, new_y);
		}

		updated_lines.push_back(std::move(list_temp));
	}

	return updated_lines;
}

std::pair<std::vector<std::vector<cv::Point2d>>, std::vector<std::vector<cv::Point2d>>> CodCalc::correct_perspective_effect(
	const std::vector<std::vector<cv::Point2d>>& list_hor_lines_in, const std::vector<std::vector<cv::Point2d>>& list_ver_lines_in,
	double xcenter, double ycenter, const std::string& method /*= "mean"*/, const std::string& scaleMode /*= "mean"*/)
{
	// Step 1: 拟合抛物线
	auto [list_coef_hor, list_hor_lines] = _para_fit_hor(list_hor_lines_in, xcenter, ycenter);
	auto [list_coef_ver, list_ver_lines] = _para_fit_ver(list_ver_lines_in, xcenter, ycenter);

	if (list_coef_hor.size() < 2) {
		return {};
	}
	if (list_coef_ver.size() < 2) {
		return {};
	}

	// Step 2: 取代表线条 (开口向上 / 下 / 左 / 右)
	std::vector<int> indices;

	// 水平方向开口向上
	indices.clear();
	for (int i = 0; i < (int)list_coef_hor.size(); i++) {
		if (list_coef_hor[i][2] > 0)
			indices.push_back(i);
	}
	if (indices.empty())
		//throw std::invalid_argument("Input error!!! No curved line open upwards !!!");
		return {};
	auto [bh1, ch1] = __get_representative_linear_coefs(list_coef_hor, indices, method);

	// 水平方向开口向下
	indices.clear();
	for (int i = 0; i < (int)list_coef_hor.size(); i++) {
		if (list_coef_hor[i][2] < 0)
			indices.push_back(i);
	}
	if (indices.empty())
		//throw std::invalid_argument("Input error!!! No curved line open downwards !!!");
		return {};
	auto [bh2, ch2] = __get_representative_linear_coefs(list_coef_hor, indices, method);

	// 垂直方向开口向右
	indices.clear();
	for (int i = 0; i < (int)list_coef_ver.size(); i++) {
		if (list_coef_ver[i][2] > 0)
			indices.push_back(i);
	}
	if (indices.empty())
		//throw std::invalid_argument("Input error!!! No curved line open rightwards !!!");
		return {};
	auto [bv1, cv1] = __get_representative_linear_coefs(list_coef_ver, indices, method);

	// 垂直方向开口向左
	indices.clear();
	for (int i = 0; i < (int)list_coef_ver.size(); i++) {
		if (list_coef_ver[i][2] < 0) indices.push_back(i);
	}
	if (indices.empty())
		//throw std::invalid_argument("Input error!!! No curved line open leftwards !!!");
		return {};
	auto [bv2, cv2] = __get_representative_linear_coefs(list_coef_ver, indices, method);

	// Step 3: 计算 4 个交点
	auto [x1, y1] = _find_cross_point_between_lines(Eigen::Vector2d(bh1, ch1), Eigen::Vector2d(bv1, cv1));
	auto [x2, y2] = _find_cross_point_between_lines(Eigen::Vector2d(bh1, ch1), Eigen::Vector2d(bv2, cv2));
	auto [x3, y3] = _find_cross_point_between_lines(Eigen::Vector2d(bh2, ch2), Eigen::Vector2d(bv1, cv1));
	auto [x4, y4] = _find_cross_point_between_lines(Eigen::Vector2d(bh2, ch2), Eigen::Vector2d(bv2, cv2));

	std::vector<cv::Point2d> source_points = {
		{x1, y1}, {x2, y2}, {x3, y3}, {x4, y4}
	};

	// Step 4: 生成 target_points
	auto [src_pts, tgt_pts] =
		generate_4_source_target_perspective_points(source_points, false, scaleMode);

	// Step 5: 计算透视变换系数
	Eigen::VectorXd pers_coef = calc_perspective_coefficients(src_pts, tgt_pts, "forward");

	// Step 6: 应用透视校正
	auto corr_hor_lines = correct_perspective_line(list_hor_lines, pers_coef);
	auto corr_ver_lines = correct_perspective_line(list_ver_lines, pers_coef);

	// Step 7: 更新中心 (如果你已有 update_center)
	corr_hor_lines = update_center(corr_hor_lines, xcenter, ycenter);
	corr_ver_lines = update_center(corr_ver_lines, xcenter, ycenter);

	return { corr_hor_lines, corr_ver_lines };
}

std::pair<double, double> CodCalc::find_center_based_vanishing_points_iteration(
	const std::vector<std::vector<cv::Point2d>>& list_hor_lines,
	const std::vector<std::vector<cv::Point2d>>& list_ver_lines, 
	int iteration /*= 2*/, const std::string& method /*= "mean"*/)
{
	// 初始中心点
	auto [xcenter, ycenter] = _find_center_based_vanishing_points_2nd_way(
		list_hor_lines, list_ver_lines);

	for (int i = 0; i < iteration; ++i)
	{
		// 第一步：透视校正
		auto [list_hor_lines1, list_ver_lines1] =
			correct_perspective_effect(list_hor_lines, list_ver_lines,
				xcenter, ycenter, method);

		// 第二步：拟合抛物线，取出更新后的线条
		auto hor_fit_result = _para_fit_hor(list_hor_lines1, xcenter, ycenter);
		list_hor_lines1 = hor_fit_result.second;

		auto ver_fit_result = _para_fit_ver(list_ver_lines1, xcenter, ycenter);
		list_ver_lines1 = ver_fit_result.second;

		// 第三步：再次计算消失点
		auto [xcenter1, ycenter1] =
			_find_center_based_vanishing_points_2nd_way(list_hor_lines1,
				list_ver_lines1);

		// Python 中是累加
		xcenter += xcenter1;
		ycenter += ycenter1;
	}

	return { xcenter, ycenter };
}
