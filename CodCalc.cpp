﻿#include "CodCalc.h"

#include <Eigen/Dense>
#include <ceres/ceres.h>

//#define  OPTIMIZED_GAUSSIAN_WINDOW


Mat CodCalc::makeWindow(int height, int width, double sigma /*= 10.0*/)
{

#ifndef OPTIMIZED_GAUSSIAN_WINDOW
	cv::Mat window(height, width, CV_64F); // 使用 double 类型存储

	double xcenter = (width - 1.0) / 2.0;
	double ycenter = (height - 1.0) / 2.0;
	double num = 2.0 * sigma * sigma;

	for (int y = 0; y < height; ++y)
	{
		for (int x = 0; x < width; ++x)
		{
			double dx = x - xcenter;
			double dy = y - ycenter;
			double value = std::exp(-(dx * dx + dy * dy) / num);
			window.at<double>(y, x) = value;
		}
	}

	return window;
#else
	// Create 1D Gaussian kernels for rows and columns
	cv::Mat gaussianX = cv::getGaussianKernel(width, sigma, CV_64F);
	cv::Mat gaussianY = cv::getGaussianKernel(height, sigma, CV_64F);

	// Create 2D Gaussian window by multiplying the 1D kernels
	cv::Mat window = gaussianY * gaussianX.t();

	// 去归一化：把中心调到 1（消掉 getGaussianKernel 的归一化常数）
	const int cy = height / 2; // 对偶数尺寸，这里等价于 floor((h-1)/2+0.5)，与公式中心一致
	const int cx = width / 2;
	double peak = window.at<double>(cy, cx);
	if (peak != 0.0) window /= peak;

	return window;

#endif
}

Mat CodCalc::applyFFTFilter(const cv::Mat& mat, double sigma, int pad, int borderType /*= cv::BORDER_REFLECT*/)
{
	CV_Assert(mat.type() == CV_64F); // 输入必须是 double
	int h = mat.rows, w = mat.cols;

	// --- 1. padding ---
	cv::Mat padded;
	cv::copyMakeBorder(mat, padded, pad, pad, pad, pad, borderType);

	int H = padded.rows, W = padded.cols;

	// --- 2. 高斯窗 ---
	cv::Mat window = makeWindow(H, W, sigma);

	// --- 3. matsign = (-1)^(x+y) ---
	cv::Mat matsign(H, W, CV_64F);
	for (int y = 0; y < H; ++y)
		for (int x = 0; x < W; ++x)
			matsign.at<double>(y, x) = ((x + y) % 2 == 0) ? 1.0 : -1.0;

	cv::Mat shifted = padded.mul(matsign);

	// --- 4. FFT ---
	cv::Mat planes[] = { shifted, cv::Mat::zeros(H, W, CV_64F) };
	cv::Mat complexMat;
	cv::merge(planes, 2, complexMat);
	cv::dft(complexMat, complexMat, cv::DFT_COMPLEX_OUTPUT);

	// --- 5. 乘以高斯窗 ---
	cv::split(complexMat, planes);
	cv::Mat realPart = planes[0].mul(window);
	cv::Mat imagPart = planes[1].mul(window);
	cv::merge(std::vector<cv::Mat>{realPart, imagPart}, complexMat);

	// --- 6. 逆 FFT ---
	cv::dft(complexMat, complexMat, cv::DFT_INVERSE | cv::DFT_SCALE | cv::DFT_COMPLEX_OUTPUT);
	cv::split(complexMat, planes);

	cv::Mat filtered = planes[0].mul(matsign); // 取实部并乘回 matsign

	// --- 7. 去掉 padding ---
	cv::Rect roi(pad, pad, w, h);
	return filtered(roi).clone();
}

cv::Mat CodCalc::normalization_fft(const cv::Mat& mat, double sigma /*= 10.0*/, int pad /*= 100*/, int borderType /*= cv::BORDER_REFLECT_101*/)
{
	// Step 1: 估计背景


	Mat mat_bck = applyFFTFilter(mat, sigma, pad, borderType);

	// Step 2: 计算背景均值
	cv::Scalar mean_val_scalar = cv::mean(mat_bck);
	double mean_val = mean_val_scalar[0];


	Mat mask = mat_bck == 0.0;

	auto mat_bck_no0 = mat_bck.clone();
    mat_bck_no0.setTo(mean_val, mask);

	Mat mat_cor;
	cv::divide(mat, mat_bck_no0, mat_cor);
	mat_cor *= mean_val;
	return mat_cor;
}

cv::Mat CodCalc::convert_chessboard_to_linepattern(const cv::Mat& mat, bool smooth /*= true*/, bool bright /*= true*/, int sigma /*= 3*/)
{
	CV_Assert(mat.channels() == 1); // 输入应为单通道灰度图
	cv::Mat img = mat.clone();

	// 1. Gaussian smoothing
	if (smooth) {
		cv::GaussianBlur(img, img, cv::Size(0, 0), sigma, sigma, cv::BORDER_REPLICATE);
	}

	// 2. Gradient
	cv::Mat grad_x, grad_y;
	cv::Sobel(img, grad_x, CV_64F, 1, 0, 3, 1, 0, cv::BORDER_DEFAULT);
	cv::Sobel(img, grad_y, CV_64F, 0, 1, 3, 1, 0, cv::BORDER_DEFAULT);

	cv::Mat abs_grad_x = cv::abs(grad_x);
	cv::Mat abs_grad_y = cv::abs(grad_y);

	cv::Mat mat_line = 0.5 * (abs_grad_x + abs_grad_y);

	// 3. crop + pad
	int crop = smooth ? 4 : 2;
	cv::Rect roi(crop, crop, mat_line.cols - 2 * crop, mat_line.rows - 2 * crop);
	cv::Mat cropped = mat_line(roi);

	cv::Mat padded;
	cv::copyMakeBorder(cropped, padded, crop, crop, crop, crop, cv::BORDER_REPLICATE);

	mat_line = padded;

	// 4. background inversion if bgr == "bright"
	if (bright) {
		double minVal, maxVal;
		cv::minMaxLoc(mat_line, &minVal, &maxVal);
		mat_line = maxVal - mat_line;
	}

	// 5. normalization
	cv::Scalar meanAbs = cv::mean(cv::abs(mat_line));
	if (meanAbs[0] > 1e-9) {
		mat_line /= meanAbs[0];
	}

	return mat_line;
}

cv::Mat CodCalc::select_roi(const cv::Mat& mat, float ratio, bool square /*= false*/)
{
	CV_Assert(mat.channels() == 1); // 假设输入是单通道图像

	int height = mat.rows;
	int width = mat.cols;

	// clip ratio to [0.05, 1.0]
	ratio = std::max(0.05f, std::min(1.0f, ratio));

	cv::Rect roi;
	if (square) {
		int c_hei = height / 2;
		int c_wid = width / 2;
		int radi = static_cast<int>(ratio * std::min(height, width)) / 2;

		int x = std::max(0, c_wid - radi);
		int y = std::max(0, c_hei - radi);
		int w = std::min(width - x, 2 * radi);
		int h = std::min(height - y, 2 * radi);

		roi = cv::Rect(x, y, w, h);
	}
	else {
		int depad_hei = static_cast<int>((height - ratio * height) / 2.0f);
		int depad_wid = static_cast<int>((width - ratio * width) / 2.0f);

		int x = depad_wid;
		int y = depad_hei;
		int w = width - 2 * depad_wid;
		int h = height - 2 * depad_hei;

		roi = cv::Rect(x, y, w, h);
	}

	return mat(roi).clone(); // 返回 ROI 副本，避免引用原图
}

cv::Mat CodCalc::make_circle_mask(int width, float ratio)
{
	cv::Mat mask = cv::Mat::zeros(width, width, CV_64F);
	int center = width / 2;
	int radius = static_cast<int>(ratio * center);
	cv::circle(mask, cv::Point(center, center), radius, cv::Scalar(1.0f), -1);
	return mask;
}

bool CodCalc::get_gauss_peak_fit(const std::vector<double>& list_data, std::vector<double>& fit_data, double& peak_c, double& offset, bool& check)
{
	int npoint = (int)list_data.size();
	std::vector<double> list_x(npoint);
	for (int i = 0; i < npoint; ++i) {
		list_x[i] = i - npoint / 2;
	}

	check = false;
	fit_data.resize(npoint);

	// 初始猜测
	double params[4] = { 1.0, 1.0, 0.0, 0.0 };

	ceres::Problem problem;
	ceres::CostFunction* cost_function =
		new ceres::AutoDiffCostFunction<GaussFunctor, ceres::DYNAMIC, 4>(
			new GaussFunctor(list_x, list_data), npoint);
	problem.AddResidualBlock(cost_function, nullptr, params);

	ceres::Solver::Options options;
	options.max_num_iterations = 50;
	options.linear_solver_type = ceres::DENSE_QR;
	options.minimizer_progress_to_stdout = false;

	ceres::Solver::Summary summary;
	ceres::Solve(options, &problem, &summary);

	if (summary.termination_type == ceres::CONVERGENCE ||
		summary.final_cost < 1e-6) {
		check = true;
	}

	double a = params[0];
	double b = params[1];
	double c = params[2];
	double d = params[3];
	for (int i = 0; i < npoint; ++i) {
		fit_data[i] = gauss_function(list_x[i], a, b, c, d);
	}
	peak_c = c;
	offset = d;
	return check;
}

struct GaussFunctor {
	GaussFunctor(const std::vector<double>& x, const std::vector<double>& y)
		: x_(x), y_(y) {
	}

	template <typename T>
	bool operator()(const T* const params, T* residuals) const {
		T a = params[0];
		T b = params[1];
		T c = params[2];
		T d = params[3];
		for (size_t i = 0; i < x_.size(); ++i) {
			T yi = T(y_[i]);
			T fi = a * exp(-pow((T(x_[i]) - c) / (2.0 * b * b), 2)) + d;
			residuals[i] = yi - fi;
		}
		return true;
	}

	const std::vector<double>& x_;
	const std::vector<double>& y_;
};



