/**
* Copyright 2018, ftdlyc <<EMAIL>>
*
* This library is free software; you can redistribute it and/or
* modify it under the terms of the GNU Lesser General Public
* License as published by the Free Software Foundation; either
* version 3 of the License, or (at your option) any later version.
*
* This library is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
* Lesser General Public License for more details.
*
* You should have received a copy of the GNU Lesser General Public
* License along with this library; if not, see <http://www.gnu.org/licenses/>.
*/

/*
% Copyright 2012. All rights reserved.
% Author: <PERSON>
%         Institute of Measurement and Control Systems (MRT)
%         Karlsruhe Institute of Technology (KIT), Germany

% This is free software; you can redistribute it and/or modify it under the
% terms of the GNU General Public License as published by the Free Software
% Foundation; either version 3 of the License, or any later version.

% This software is distributed in the hope that it will be useful, but WITHOUT ANY
% WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A
% PARTICULAR PURPOSE. See the GNU General Public License for more details.

% You should have received a copy of the GNU General Public License along with
% this software; if not, write to the Free Software Foundation, Inc., 51 Franklin
% Street, Fifth Floor, Boston, MA 02110-1301, USA
*/

#pragma once
#ifndef LIBCBDETECT_GROW_BOARD_H
#define LIBCBDETECT_GROW_BOARD_H

#include <vector>

#include "libcbdetect/config.h"

namespace cbdetect {

enum GrowType {
  GrowType_Failure = 0,
  GrowType_Inside,
  GrowType_Boundary,
};

LIBCBDETECT_DLL_DECL GrowType grow_board(const Corner& corners, std::vector<int>& used, Board& board,
                                         std::vector<cv::Point2i>& proposal, int direction, const Params& params);

} // namespace cbdetect

#endif //LIBCBDETECT_GROW_BOARD_H
